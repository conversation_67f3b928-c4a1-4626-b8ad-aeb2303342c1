{"name": "@element-plus/icons-vue", "version": "2.3.2", "description": "Vue components of Element Plus Icons collection.", "type": "module", "license": "MIT", "homepage": "https://element-plus.org/", "repository": {"type": "git", "url": "git+https://github.com/element-plus/element-plus-icons.git", "directory": "packages/vue"}, "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}, "./global": {"types": "./dist/types/global.d.ts", "require": "./dist/global.cjs", "import": "./dist/global.js"}, "./*": "./*"}, "typesVersions": {"*": {"*": ["./*", "./dist/types/*"]}}, "sideEffects": false, "unpkg": "dist/index.iife.min.js", "jsdelivr": "dist/index.iife.min.js", "peerDependencies": {"vue": "^3.2.0"}, "devDependencies": {"@pnpm/find-workspace-dir": "^1000.1.2", "@pnpm/find-workspace-packages": "^6.0.9", "@pnpm/logger": "^1001.0.0", "@types/fs-extra": "^11.0.4", "@types/node": "^24.2.0", "camelcase": "^8.0.0", "chalk": "^5.5.0", "consola": "^3.4.2", "esbuild": "^0.25.8", "esbuild-plugin-globals": "^0.2.0", "fast-glob": "^3.3.3", "fs-extra": "^11.3.0", "npm-run-all": "^4.1.5", "prettier": "^3.6.2", "tsx": "^4.20.3", "typescript": "^5.9.2", "unplugin-vue": "^7.0.1", "vue": "^3.5.18", "vue-tsc": "^3.0.5", "@element-plus/icons-svg": "2.3.2"}}