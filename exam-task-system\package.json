{"name": "exam-task-system", "version": "1.0.0", "private": true, "type": "commonjs", "engines": {"node": "^20.19.0 || >=22.12.0"}, "main": "electron/main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "vercel-build": "vite build", "electron:dev": "node start-electron.js", "electron:build": "vite build && electron-builder", "electron:start": "npx electron ."}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@vueuse/core": "^13.6.0", "axios": "^1.11.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "electron-is-dev": "^3.0.1", "element-plus": "^2.10.7", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "qrcode": "^1.5.4", "vue": "^3.5.18", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "vue3-toastify": "^0.2.8"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@vitejs/plugin-vue": "^6.0.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "electron": "^30.5.1", "electron-builder": "^24.13.3", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}, "build": {"appId": "com.examtask.app", "productName": "考研任务系统1", "directories": {"output": "dist_electron6"}, "files": ["dist/**/*", "electron/**/*", "!api/**/*", "!backend/**/*", "!backend_deploy_new/**/*", "!start-electron.js", "!*.py", "!*.ps1", "!*.md", "!vercel.json", "!tailwind.config.js", "!postcss.config.js", "!vite.config.js", "!jsconfig.json"], "win": {"target": ["nsis"]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true}}}