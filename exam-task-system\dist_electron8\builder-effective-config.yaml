directories:
  output: dist_electron8
  buildResources: build
appId: com.examtask.app
productName: 考研任务系统1
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - '!api/**/*'
      - '!backend/**/*'
      - '!backend_deploy_new/**/*'
      - '!start-electron.js'
      - '!*.py'
      - '!*.ps1'
      - '!*.md'
      - '!vercel.json'
      - '!tailwind.config.js'
      - '!postcss.config.js'
      - '!vite.config.js'
      - '!jsconfig.json'
win:
  target:
    - nsis
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
electronVersion: 30.5.1
