<template>
  <div class="tasks-page">
    <!-- 艺术化顶部区域 -->
    <div class="hero-section">
      <div class="hero-background">
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
          <div class="shape shape-4"></div>
        </div>
        <div class="gradient-overlay"></div>
      </div>
      
      <div class="hero-content">
        <div class="stats-display">
          <div class="main-stats">
            <div class="stats-number">
              <span class="number-large">{{ selectedTasksCount }}</span>
              <span class="number-divider">/</span>
              <span class="number-small">{{ dailyTaskLimit }}</span>
            </div>
            <div class="stats-label">今日任务选择</div>
          </div>
          
          <div class="progress-circle">
            <svg class="progress-ring" width="80" height="80">
              <circle
                class="progress-ring-bg"
                stroke="rgba(255, 255, 255, 0.2)"
                stroke-width="4"
                fill="transparent"
                r="34"
                cx="40"
                cy="40"
              />
              <circle
                class="progress-ring-fill"
                stroke="url(#gradient)"
                stroke-width="4"
                fill="transparent"
                r="34"
                cx="40"
                cy="40"
                :stroke-dasharray="213.6"
                :stroke-dashoffset="progressOffset"
                stroke-linecap="round"
              />
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:1" />
                </linearGradient>
              </defs>
            </svg>
            <div class="progress-text">{{ progressPercentage }}%</div>
          </div>
        </div>
        
        <div class="motivational-section">
          <div class="motto-text">
            <h3 class="main-motto">每一步努力，都是通往梦想的阶梯</h3>
            <p class="sub-motto">每天至少选择{{ dailyTaskLimit }}个任务，可以多选！✨</p>
          </div>
          <div class="achievement-badges">
            <div class="badge badge-streak">
              <div class="badge-icon">🔥</div>
              <div class="badge-text">连续{{ streakDays }}天</div>
            </div>
            <div class="badge badge-points">
              <div class="badge-icon">⭐</div>
              <div class="badge-text">今日+{{ todayPoints }}分</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优化后的筛选区域 -->
    <div class="filter-section">
      <div class="filter-container">
        <div class="filter-header">
          <div class="filter-icon">🔍</div>
          <h3 class="filter-title">智能筛选</h3>
          <div class="filter-subtitle">找到最适合你的任务</div>
        </div>
        
        <div class="filter-content">
          <div class="filter-row">
            <div class="filter-group">
              <label class="filter-label">📚 科目</label>
              <el-select v-model="filterSubject" placeholder="选择科目" class="custom-select">
                <el-option label="全部科目" value="" />
                <el-option label="数学" value="数学" />
                <el-option label="英语" value="英语" />
                <el-option label="政治" value="政治" />
                <el-option label="专业课" value="专业课" />
                <el-option label="逻辑" value="逻辑" />
              </el-select>
            </div>
            
            <div class="filter-group">
              <label class="filter-label">⏰ 类型</label>
              <el-select v-model="filterType" placeholder="任务类型" class="custom-select">
                <el-option label="全部类型" value="" />
                <el-option label="每日任务" value="daily" />
                <el-option label="周常任务" value="weekly" />
                <el-option label="专项任务" value="special" />
              </el-select>
            </div>
            
            <div class="filter-group">
              <label class="filter-label">🎯 排序</label>
              <el-select v-model="sortBy" placeholder="排序方式" class="custom-select">
                <el-option label="最新发布" value="newest" />
                <el-option label="截止日期" value="deadline" />
                <el-option label="积分高低" value="points_desc" />
              </el-select>
            </div>
          </div>
          
          <div class="filter-actions">
            <el-button type="primary" class="filter-btn apply-btn" @click="applyFilters">
              <span class="btn-icon">✨</span>
              应用筛选
            </el-button>
            <el-button class="filter-btn reset-btn" @click="resetFilters">
              <span class="btn-icon">🔄</span>
              重置筛选
            </el-button>
            <el-button class="filter-btn danger-btn" @click="confirmResetStudentData">
              <span class="btn-icon">🗑️</span>
              清空数据
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 简洁任务计数区域 -->
    <div class="task-count-section">
      <div class="count-container">
        <div class="count-content">
          <span class="count-number">{{ filteredTasks.length }}</span>
          <span class="count-label">个任务待领取</span>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <el-skeleton :loading="loading" animated :count="4" :throttle="500">
      <template #template>
        <div class="task-list">
          <div v-for="i in 4" :key="i" class="task-card skeleton">
            <el-skeleton-item variant="p" style="width: 50%" />
            <el-skeleton-item variant="text" style="width: 100%" />
            <el-skeleton-item variant="text" style="width: 100%" />
            <div style="display: flex; justify-content: space-between; margin-top: 16px;">
              <el-skeleton-item variant="text" style="width: 30%" />
              <el-skeleton-item variant="text" style="width: 30%" />
            </div>
            <el-skeleton-item variant="button" style="width: 100%; margin-top: 16px;" />
          </div>
        </div>
      </template>
      
      <template #default>
        <div class="task-list">
          <div v-for="task in filteredTasks" :key="task.id" class="task-card" :class="getSubjectCardClass(task.subject)">
            <!-- 科目标签 -->
            <div class="subject-tag" :class="getSubjectClass(task.subject)">
              {{ task.subject }}
            </div>
            
            <!-- 任务标题和类型 -->
            <div class="title-row">
              <h3 class="task-title">{{ task.title }}</h3>
              <span class="task-type-tag">{{ getActionLabel(task.type) }}</span>
            </div>
            
            <!-- 任务描述 -->
            <p class="task-description">{{ task.description }}</p>
            
            <!-- 任务详情 -->
            <div class="task-details">
              <div class="detail-row">
                <span class="detail-label">目标</span>
                <span class="detail-value">{{ task.target || '50 个' }}</span>
              </div>
              
              <div class="detail-row">
                <span class="detail-label">奖励</span>
                <span class="detail-value reward">{{ task.points }} 积分</span>
              </div>
              
              <div class="detail-row">
                <span class="detail-label">截止</span>
                <span class="detail-value deadline">{{ formatDate(task.dueDate) }}</span>
              </div>
            </div>
            
            <!-- 任务操作 -->
            <div class="task-actions">
              <el-button
                type="primary"
                class="action-button primary-btn full-width"
                :class="getPrimaryButtonClass(task.subject)"
                @click="acceptTask(task)"
              >
                领取任务
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-skeleton>

    <!-- 分页 -->
    <div class="pagination" v-if="!loading && filteredTasks.length > 0">
      <span class="page-info">共 {{ filteredTasks.length }} 条</span>
      <el-select v-model="pageSize" size="small" class="page-size-select">
        <el-option label="10条/页" value="10" />
        <el-option label="20条/页" value="20" />
        <el-option label="50条/页" value="50" />
      </el-select>
      <el-pagination
        background
        layout="prev, pager, next"
        :total="filteredTasks.length"
        :page-size="Number(pageSize)"
        @current-change="handlePageChange"
      />
      <div class="page-jumper">
        <span>前往</span>
        <el-input v-model="currentPage" size="small" class="page-input" />
        <span>页</span>
      </div>
    </div>
    
    <div v-if="!loading && filteredTasks.length === 0" class="empty-state">
      <el-empty description="暂无可用任务" />
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="detailsDialogVisible"
      :title="selectedTask ? selectedTask.title : '任务详情'"
      width="80%"
    >
      <div v-if="selectedTask" class="task-details-dialog">
        <div class="task-info">
          <p><strong>任务类型:</strong> {{ getTaskTypeLabel(selectedTask.type) }}</p>
          <p><strong>难度等级:</strong> 
            <el-rate
              v-model="selectedTask.difficulty"
              disabled
              text-color="#ff9900"
              score-template="{value}"
              :max="3"
            />
          </p>
          <p><strong>截止日期:</strong> {{ formatDate(selectedTask.dueDate) }}</p>
          <p><strong>奖励积分:</strong> {{ selectedTask.points }} 分</p>
        </div>
        
        <div class="task-content">
          <h3>任务描述</h3>
          <p>{{ selectedTask.description }}</p>
          
          <h3>任务要求</h3>
          <ul>
            <li v-for="(req, index) in selectedTask.requirements" :key="index">
              {{ req }}
            </li>
          </ul>
          
          <h3>完成标准</h3>
          <p>{{ selectedTask.completionCriteria }}</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailsDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            @click="acceptTask(selectedTask)"
          >
            接受任务
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 任务限制提示对话框 -->
    <el-dialog
      v-model="limitDialogVisible"
      title="每日任务已满"
      width="80%"
    >
      <div class="limit-dialog-content">
        <el-result
          icon="warning"
          title="每日任务已达上限"
          sub-title="您今天已经选择了5个任务，明天再来选择新任务吧！"
        >
          <template #extra>
            <el-button type="primary" @click="goToMyTasks">查看我的任务</el-button>
            <el-button @click="limitDialogVisible = false">关闭</el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useAuthStore } from '@/stores/auth';
import { useSyncStore } from '@/stores/sync';
import { useStudentStore } from '@/stores/student';

// 组件状态
const router = useRouter();
const authStore = useAuthStore();
const syncStore = useSyncStore();
const studentStore = useStudentStore();
const filterType = ref('');
const filterSubject = ref('');
const sortBy = ref('newest');

const detailsDialogVisible = ref(false);
const limitDialogVisible = ref(false);
const selectedTask = ref(null);
const tasks = ref([]);
const pageSize = ref('10');
const currentPage = ref(1);
const dailyTaskLimit = 5; // 每天最少需要选择的任务数量（可以多选）
const selectedTasksCount = ref(0);
const loading = ref(false);
const todayPoints = ref(0); // 今日获得积分

// 连续天数：从今天开始向前逐日统计，直到遇到无完成任务的日期
const streakDays = computed(() => {
  const tasks = studentStore.myTasks || [];
  const hasCompletedOn = (date) => {
    const dateStr = date.toDateString();
    return tasks.some(task => {
      if (task.status !== 'completed') return false;
      const completed = task.completedAt || task.completionDate || task.endTime || task.reviewDate;
      if (!completed) return false;
      const dt = new Date(completed);
      return !isNaN(dt.getTime()) && dt.toDateString() === dateStr;
    });
  };

  let streak = 0;
  const today = new Date();
  // 最多回溯一年以防极端数据
  for (let i = 0; i < 366; i++) {
    const d = new Date(today);
    d.setDate(today.getDate() - i);
    if (hasCompletedOn(d)) streak++;
    else break;
  }
  return streak;
});

// 任务类型映射
const taskTypes = {
  daily: '每日任务',
  weekly: '周常任务',
  special: '专项任务'
};

// 排序方法映射
const sortMethods = {
  newest: (a, b) => new Date(b.createdAt || Date.now()) - new Date(a.createdAt || Date.now()),
  deadline: (a, b) => new Date(a.deadline) - new Date(b.deadline),
  points_desc: (a, b) => b.points - a.points
};

// 计算进度百分比
const progressPercentage = computed(() => {
  return Math.round((selectedTasksCount.value / dailyTaskLimit) * 100);
});

// 计算进度环偏移量
const progressOffset = computed(() => {
  const circumference = 2 * Math.PI * 34; // 2πr
  const offset = circumference * (1 - selectedTasksCount.value / dailyTaskLimit);
  return offset;
});

// {{ AURA-X: Modify - 添加实时同步初始化. Approval: 寸止(ID:1734682800). }}
// 实时数据更新处理
const handleTasksUpdate = (event) => {
  console.log('接收到任务实时更新:', event.detail);
  loadTasks(); // 重新加载任务数据
};

const handlePointsUpdate = (event) => {
  console.log('接收到积分实时更新:', event.detail);
  calculateTodayPoints(); // 重新计算积分
};

// 生命周期钩子
onMounted(async () => {
  // 初始化同步服务
  if (!syncStore.isInitialized) {
    await syncStore.initSync();
  }

  // 禁用实时同步订阅
  // syncStore.startUserSubscriptions();

  // 禁用实时数据更新事件监听
  // window.addEventListener('tasks-updated', handleTasksUpdate);
  window.addEventListener('points-updated', handlePointsUpdate);

  // 先初始化学生数据，再加载任务数据
  await studentStore.initStudentData();
  await loadTasks();
  calculateSelectedTasksCount();
  calculateTodayPoints();

  // 监听学生任务数据变化，自动更新统计
  watch(() => studentStore.myTasks, () => {
    calculateSelectedTasksCount();
    calculateTodayPoints();
  }, { deep: true });

  // 实时同步暂时禁用
  // syncStore.subscribeToCollection('tasks', {}, (data) => {
  //   console.log('任务广场接收到任务更新:', data);
  //   calculateSelectedTasksCount();
  //   calculateTodayPoints();
  // });

  // syncStore.subscribeToCollection('bindings', {}, (data) => {
  //   console.log('任务广场接收到绑定信息更新:', data);
  //   calculateTodayPoints();
  // });

  // syncStore.subscribeToCollection('student-stats', {}, (data) => {
  //   console.log('任务广场接收到统计数据更新:', data);
  //   calculateSelectedTasksCount();
  //   calculateTodayPoints();
  // });
});

// 组件卸载时清理
onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('tasks-updated', handleTasksUpdate);
  window.removeEventListener('points-updated', handlePointsUpdate);

  // 停止订阅（可选，如果需要在页面切换时保持连接可以不调用）
  // syncStore.stopAllSubscriptions();
});

// 测试按钮已移除

// {{ AURA-X: Modify - 使用统一的学生端数据源. Approval: 寸止(ID:1734684000). }}
// 加载任务数据
const loadTasks = async () => {
  loading.value = true;
  try {
    // 使用统一的学生端数据源
    await studentStore.loadAvailableTasks();
    tasks.value = studentStore.availableTasks;
  } catch (error) {
    console.error('加载任务数据失败:', error);
    ElMessage.error('加载任务数据失败，请检查网络连接');
    tasks.value = [];
  } finally {
    loading.value = false;
  }
};

// {{ AURA-X: Delete - 移除localStorage备用机制. Approval: 寸止(ID:1734682800). }}
// 已移除fallbackToLocalTasks函数，统一使用CloudBase数据源

// {{ AURA-X: Modify - 修正今日任务选择数量计算逻辑. Approval: 寸止(ID:1734683400). }}
// 计算已选择的任务数量
const calculateSelectedTasksCount = () => {
  try {
    // 使用统一数据源计算今日接受的每日任务数量（兼容字段 + 同日判断）
    const today = new Date();
    const isSameDay = (d) => {
      if (!d) return false;
      const dt = new Date(d);
      return !isNaN(dt.getTime()) && dt.toDateString() === today.toDateString();
    };

    console.log('所有我的任务:', studentStore.myTasks);

    // 修正逻辑：只统计今天接受的每日任务（兼容 acceptedDate/acceptedAt/createdAt）
    const todayDailyTasks = studentStore.myTasks.filter(task => {
      const accepted = task.acceptedDate || task.acceptedAt || task.createdAt;
      const isDaily = task.type === 'daily';
      const isToday = isSameDay(accepted);

      console.log('任务筛选:', {
        title: task.title,
        type: task.type,
        acceptedDate: task.acceptedDate,
        acceptedAt: task.acceptedAt,
        createdAt: task.createdAt,
        status: task.status,
        isDaily,
        isToday
      });

      return isDaily && isToday;
    });

    selectedTasksCount.value = todayDailyTasks.length;
    console.log('今日已选择任务数量:', selectedTasksCount.value);
    console.log('今日每日任务:', todayDailyTasks);
  } catch (error) {
    console.error('计算已选择任务数量失败:', error);
    selectedTasksCount.value = 0;
  }
};

// {{ AURA-X: Modify - 使用统一的学生端数据源计算积分. Approval: 寸止(ID:1734684000). }}
// 计算今日获得的积分
const calculateTodayPoints = () => {
  // 使用统一数据源的计算属性
  todayPoints.value = studentStore.todayPoints;
  console.log('今日积分计算结果:', todayPoints.value);
  console.log('学生任务数据:', studentStore.myTasks.length);
};

// 根据筛选条件过滤任务
const filteredTasks = computed(() => {
  let result = [...tasks.value];
  
  // 按类型筛选
  if (filterType.value) {
    result = result.filter(task => task.type === filterType.value);
  }
  
  // 按科目筛选
  if (filterSubject.value) {
    // “数学”筛选时同时包含“逻辑”
    if (filterSubject.value === '数学') {
      result = result.filter(task => task.subject === '数学' || task.subject === '逻辑');
    } else {
      result = result.filter(task => task.subject === filterSubject.value);
    }
  }
  
  // 排序
  const sortMethod = sortMethods[sortBy.value];
  if (sortMethod) {
    result.sort(sortMethod);
  }
  
  return result;
});

// 获取任务类型标签
const getTaskTypeLabel = (type) => {
  return taskTypes[type] || '未知类型';
};

// 获取科目样式类
const getSubjectClass = (subject) => {
  const subjectMap = {
    '数学': 'math',
    '英语': 'english',
    '政治': 'politics',
    '专业课': 'major',
    '逻辑': 'logic'
  };
  return subjectMap[subject] || '';
};

// 获取科目卡片样式类
const getSubjectCardClass = (subject) => {
  const subjectMap = {
    '数学': 'math-subject',
    '英语': 'english-subject',
    '政治': 'politics-subject',
    '专业课': 'major-subject',
    '逻辑': 'logic-subject'
  };
  return subjectMap[subject] || '';
};

// 获取主按钮样式类
const getPrimaryButtonClass = (subject) => {
  const subjectMap = {
    '数学': 'btn-math',
    '英语': 'btn-english',
    '政治': 'btn-politics',
    '专业课': 'btn-major',
    '逻辑': 'btn-logic'
  };
  return subjectMap[subject] || '';
};



// 获取操作按钮标签
const getActionLabel = (type) => {
  const labels = {
    'daily': '练习',
    'weekly': '复习',
    'special': '学习'
  };
  return labels[type] || '查看';
};

// 获取操作按钮样式类
const getActionClass = (type) => {
  const classes = {
    'daily': 'practice',
    'weekly': 'review',
    'special': 'study'
  };
  return classes[type] || '';
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return 'N/A';
  return `${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}`;
};

// 格式化时间
const formatTime = (dateString) => {
  const date = new Date(dateString);
  return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
};

// 查看任务详情
const viewTaskDetails = (task) => {
  selectedTask.value = task;
  detailsDialogVisible.value = true;
};

// 应用筛选
const applyFilters = () => {
  ElMessage.success('筛选条件已应用');
};

// 重置筛选
const resetFilters = () => {
  filterType.value = '';
  filterSubject.value = '';
  sortBy.value = 'newest';
  ElMessage.info('筛选条件已重置');
};

// {{ AURA-X: Add - 添加学生端数据重置功能. Approval: 寸止(ID:1734683400). }}
// 确认重置学生数据
const confirmResetStudentData = () => {
  ElMessageBox.confirm(
    '确定要清空您的所有数据吗？这将删除您的所有任务记录、完成记录和积分记录。此操作不可恢复！',
    '清空学生数据',
    {
      confirmButtonText: '确定清空',
      cancelButtonText: '取消',
      type: 'error',
      dangerouslyUseHTMLString: true
    }
  ).then(() => {
    resetStudentData();
  }).catch(() => {});
};

// 重置学生数据
const resetStudentData = async () => {
  try {
    loading.value = true;

    // 调用清空学生数据API
    const response = await fetch(`/api/tasks/student/${authStore.user.id}/clear`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      ElMessage.success(`清空成功！删除了 ${result.data.deletedTasks} 个任务、${result.data.deletedCompletions} 个完成记录、${result.data.deletedPoints} 个积分记录`);

      // 刷新页面数据
      await loadTasks();
      calculateSelectedTasksCount();
      calculateTodayPoints();
    } else {
      const error = await response.json();
      ElMessage.error(error.message || '清空失败');
    }
  } catch (error) {
    console.error('清空学生数据失败:', error);
    ElMessage.error('清空失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 跳转到我的任务页面
const goToMyTasks = () => {
  router.push('/student/my-tasks');
};

// {{ AURA-X: Modify - 使用统一的学生端数据源接受任务. Approval: 寸止(ID:1734684000). }}
// 接受任务
const acceptTask = async (task) => {
  if (!task) return;

  try {
    loading.value = true;

    // 使用统一的学生端数据源接受任务
    const success = await studentStore.acceptTask(task);

    if (success) {
      // 刷新任务列表
      await loadTasks();

      // 延迟计算，确保数据已更新
      setTimeout(() => {
        calculateSelectedTasksCount();
        calculateTodayPoints();
      }, 100);

      // 关闭详情对话框
      detailsDialogVisible.value = false;

      console.log('任务接受成功，当前我的任务数量:', studentStore.myTasks.length);
    }

  } catch (error) {
    console.error('接受任务失败:', error);
    ElMessage.error('接受任务失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 本地处理接受任务（API调用失败时的备用方案）
const fallbackAcceptTask = (task, myTasks) => {
  // 创建我的任务对象
  const myTask = createMyTaskObject(task);
  
  // 添加任务到我的任务列表
  myTasks.push(myTask);
  
  // 保存到本地存储
  localStorage.setItem('myTasks', JSON.stringify(myTasks));
  
  // 更新已选择的任务数量
  if (task.type === 'daily') {
    selectedTasksCount.value++;
  }
  
  // 更新全局任务状态
  const globalTasks = JSON.parse(localStorage.getItem('globalTasks') || '[]');
  const taskIndex = globalTasks.findIndex(t => t.id === task.id);
  if (taskIndex !== -1) {
    // 记录领取该任务的学生
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
    const userId = currentUser.id || 'student-' + Date.now(); // 如果没有用户ID，生成一个临时ID
    
    globalTasks[taskIndex].acceptedBy = globalTasks[taskIndex].acceptedBy || [];
    globalTasks[taskIndex].acceptedBy.push({
      userId: userId,
      username: currentUser.username || '学生用户',
      acceptedAt: new Date().toISOString()
    });
    
    localStorage.setItem('globalTasks', JSON.stringify(globalTasks));
  }
  
  // 从当前任务列表中移除已领取的任务
  tasks.value = tasks.value.filter(t => t.id !== task.id);
  
  ElMessage.success('任务领取成功！');
  
  // 关闭详情对话框
  detailsDialogVisible.value = false;
};

// 创建我的任务对象
const createMyTaskObject = (task) => {
  // 获取当前用户信息
  const authStore = useAuthStore();
  const currentUser = authStore.user;

  return {
    // 基础任务信息 - 优先使用数据库ID
    id: task.id || task._id || `fixed-task-${Date.now()}`,
    title: task.title,
    description: task.description,
    type: task.type,
    subject: task.subject,
    difficulty: task.difficulty,
    deadline: task.deadline || task.dueDate,
    points: task.points,
    target: task.target,
    requirements: task.requirements || [],
    completionCriteria: task.completionCriteria,

    // 关联用户信息
    studentId: currentUser?.id || null,

    // 我的任务特有字段
    acceptedDate: new Date().toISOString(),
    status: 'in_progress', // in_progress, completed, overdue, submitted
    progress: 0, // 进度百分比 0-100
    startTime: null, // 开始时间
    endTime: null, // 结束时间
    submitTime: null, // 提交时间

    // 任务执行记录
    workLog: [], // 工作日志
    submissions: [], // 提交记录
    
    // 统计信息
    totalTimeSpent: 0, // 总耗时（分钟）
    lastActiveTime: new Date().toISOString(), // 最后活跃时间
    
    // 任务痕迹系统相关
    traces: [], // 任务痕迹记录
    repairStones: 0, // 可用修复石数量
    
    // 扩展字段
    notes: '', // 个人笔记
    tags: [], // 个人标签
    priority: 'normal', // 优先级: low, normal, high
    reminder: null, // 提醒设置
    
    // 从任务广场继承的完整信息
    originalTask: { ...task } // 保留原始任务完整信息
  };
};

// 更新任务统计
const updateTaskStats = (newTask) => {
  try {
    const stats = JSON.parse(localStorage.getItem('taskStats') || '{}');
    
    // 更新统计数据
    stats.totalTasks = (stats.totalTasks || 0) + 1;
    stats.tasksBySubject = stats.tasksBySubject || {};
    stats.tasksByType = stats.tasksByType || {};
    stats.tasksByDifficulty = stats.tasksByDifficulty || {};
    
    // 按科目统计
    const subject = newTask.subject;
    stats.tasksBySubject[subject] = (stats.tasksBySubject[subject] || 0) + 1;
    
    // 按类型统计
    const type = newTask.type;
    stats.tasksByType[type] = (stats.tasksByType[type] || 0) + 1;
    
    // 按难度统计
    const difficulty = newTask.difficulty;
    stats.tasksByDifficulty[difficulty] = (stats.tasksByDifficulty[difficulty] || 0) + 1;
    
    // 更新最后领取时间
    stats.lastAcceptTime = new Date().toISOString();
    
    // 保存统计数据
    localStorage.setItem('taskStats', JSON.stringify(stats));
  } catch (error) {
    console.error('更新任务统计失败:', error);
  }
};

</script>

<style scoped>
.tasks-page {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  max-width: 1200px;
  margin: 0 auto;
}

/* 艺术化顶部区域样式 */
.hero-section {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 16px;
  padding: 24px 20px;
  margin-bottom: 24px;
  overflow: hidden;
  min-height: 160px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 60px;
  height: 60px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 40px;
  height: 40px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

.shape-4 {
  width: 30px;
  height: 30px;
  top: 30%;
  right: 40%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.hero-content {
  position: relative;
  z-index: 2;
  color: white;
}

.stats-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.main-stats {
  flex: 1;
}

.stats-number {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.number-large {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.number-divider {
  font-size: 32px;
  font-weight: 300;
  margin: 0 4px;
  opacity: 0.8;
}

.number-small {
  font-size: 28px;
  font-weight: 400;
  opacity: 0.9;
}

.stats-label {
  font-size: 16px;
  font-weight: 500;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.progress-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.progress-text {
  position: absolute;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.motivational-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.motto-text {
  flex: 1;
}

.main-motto {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  line-height: 1.3;
}

.sub-motto {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
  font-style: italic;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.achievement-badges {
  display: flex;
  gap: 12px;
}

.badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.badge-icon {
  font-size: 16px;
}

.badge-text {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.badge-streak {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 优化后的筛选区域样式 */
.filter-section {
  margin-bottom: 24px;
}

.filter-container {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(102, 126, 234, 0.1);
  position: relative;
  overflow: hidden;
}

.filter-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.filter-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.filter-icon {
  font-size: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.filter-subtitle {
  font-size: 12px;
  color: #7f8c8d;
  margin-left: auto;
  font-style: italic;
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-label {
  font-size: 13px;
  font-weight: 500;
  color: #34495e;
  display: flex;
  align-items: center;
  gap: 4px;
}

.custom-select {
  width: 100%;
}

.custom-select :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.custom-select :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.custom-select :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.filter-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 8px;
  border-top: 1px solid #f0f2f5;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.apply-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.reset-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.reset-btn:hover {
  background: #e9ecef;
  color: #495057;
  transform: translateY(-1px);
}

.danger-btn {
  background: #fff5f5;
  color: #e53e3e;
  border: 1px solid #fed7d7;
}

.danger-btn:hover {
  background: #fed7d7;
  color: #c53030;
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 14px;
}

/* 简洁任务计数区域 */
.task-count-section {
  margin-bottom: 20px;
}

.count-container {
  background: white;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #667eea;
}

.count-content {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.count-number {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
}

.count-label {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
}

/* 任务列表样式 */
.task-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.task-card {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
  overflow: hidden;
  border-top: 3px solid #ddd;
  min-height: 160px;
  width: 100%;
  box-sizing: border-box;
}

/* 根据科目设置不同的顶部边框颜色 */
.task-card.math-subject {
  border-top-color: #ffc107;
}

.task-card.english-subject {
  border-top-color: #f56c6c;
}

.task-card.politics-subject {
  border-top-color: #ff9500;
}

.task-card.major-subject {
  border-top-color: #67c23a;
}

.task-card.logic-subject {
  border-top-color: #0088ff;
}

/* 保障逻辑科目的主按钮在任何打包顺序下都为蓝色 */
.task-card.logic-subject .primary-btn {
  background-color: #0088ff !important;
  border-color: #0088ff !important;
}

.task-card.logic-subject {
  border-top-color: #0088ff;
}

.task-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

/* 科目标签 */
.subject-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  color: white;
  margin-bottom: 8px;
}

.subject-tag.math {
  background-color: #ffc107;
}

.subject-tag.english {
  background-color: #f56c6c;
}

.subject-tag.politics {
  background-color: #ff9500;
}

.subject-tag.major {
  background-color: #67c23a;
}

.subject-tag.logic {
  background-color: #0088ff;
}

/* 任务标题和类型行 */
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.task-title {
  margin: 0;
  font-size: 15px;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
  flex: 1;
}

.task-type-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  margin-left: 8px;
  border: 1px solid #ddd;
}

/* 任务描述 */
.task-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 32px;
}

/* 任务详情 */
.task-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
  margin-bottom: 10px;
}

.detail-row {
  display: flex;
  flex-direction: column;
}

.detail-label {
  color: #999;
  font-size: 10px;
  margin-bottom: 2px;
}

.detail-value {
  color: #333;
  font-weight: 500;
  font-size: 12px;
}

.detail-value.reward {
  color: #ff5252;
}

.detail-value.deadline {
  color: #e6a23c;
}

/* 任务操作 */
.task-actions {
  display: flex;
  width: 100%;
}

.action-button {
  font-size: 11px;
  height: 30px;
  border-radius: 4px;
}

.full-width {
  width: 100%;
}

.primary-btn {
  background-color: #000;
  border-color: #000;
}

.primary-btn:hover {
  background-color: #333;
  border-color: #333;
}

/* 不同科目的按钮颜色 */
.btn-math {
  background-color: #ffc107 !important;
  border-color: #ffc107 !important;
  color: #000 !important;
}

.btn-math:hover {
  background-color: #e0a800 !important;
  border-color: #e0a800 !important;
  color: #000 !important;
}

.btn-english {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
}

.btn-english:hover {
  background-color: #f45454 !important;
  border-color: #f45454 !important;
}

.btn-politics {
  background-color: #ff9500 !important;
  border-color: #ff9500 !important;
}

.btn-politics:hover {
  background-color: #e6850e !important;
  border-color: #e6850e !important;
}

.btn-major {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
}

.btn-major:hover {
  background-color: #5daf34 !important;
  border-color: #5daf34 !important;
}

.btn-logic {
  background-color: #0088ff !important;
  border-color: #0088ff !important;
}

.btn-logic:hover {
  background-color: #0077dd !important;
  border-color: #0077dd !important;
}

.secondary-btn {
  color: #333;
  border-color: #ddd;
  background-color: #f5f5f5;
}

.secondary-btn:hover {
  background-color: #eee;
}

/* 分页样式 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.page-info {
  font-size: 13px;
  color: #909399;
}

.page-size-select {
  width: 100px;
}

.page-jumper {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #606266;
}

.page-input {
  width: 50px;
  margin: 0 5px;
}

/* 空状态 */
.empty-state {
  margin-top: 40px;
  text-align: center;
}

/* 任务详情对话框 */
.task-details-dialog {
  padding: 20px;
}

.task-info {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.task-content h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.task-content ul {
  padding-left: 20px;
}

.task-content li {
  margin-bottom: 5px;
}

/* 任务限制提示对话框 */
.limit-dialog-content {
  padding: 20px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .hero-section {
    padding: 20px 16px;
    min-height: 140px;
  }
  
  .stats-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .number-large {
    font-size: 36px;
  }
  
  .number-divider {
    font-size: 24px;
  }
  
  .number-small {
    font-size: 20px;
  }
  
  .main-motto {
    font-size: 16px;
  }
  
  .motivational-section {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .achievement-badges {
    align-self: flex-end;
  }
  
  .filter-container {
    padding: 16px;
  }
  
  .filter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .filter-subtitle {
    margin-left: 0;
  }
  
  .filter-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .filter-actions {
    flex-direction: column;
  }
  
  .filter-btn {
    width: 100%;
    justify-content: center;
  }
  
  .count-container {
    padding: 12px 16px;
  }
  
  .count-number {
    font-size: 24px;
  }
  
  .count-label {
    font-size: 14px;
  }
  
  .task-list {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .task-card {
    min-height: 140px;
  }
  
  .pagination {
    flex-direction: column;
    align-items: center;
  }
}

@media (min-width: 481px) {
  .task-list {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 12px;
  }
  
  .task-card {
    min-height: 160px;
  }
}

@media (min-width: 768px) {
  .task-list {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 14px;
  }
  
  .filter-container {
    padding: 20px;
  }
  
  .filter-header {
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
  
  .filter-subtitle {
    margin-left: auto;
  }
  
  .filter-row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .filter-actions {
    flex-direction: row;
  }
  
  .filter-btn {
    width: auto;
  }
}
</style>
