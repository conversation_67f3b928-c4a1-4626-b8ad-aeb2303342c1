import api from './index'

export const taskAPI = {
  // 创建任务（监督者）
  createTask: (data) => api.post('/tasks', data),

  // 获取任务列表
  getTaskList: (params) => api.get('/tasks', { params }),

  // 获取任务详情
  getTaskDetail: (taskId) => api.get(`/tasks/${taskId}`),

  // 更新任务（监督者）
  updateTask: (taskId, data) => api.put(`/tasks/${taskId}`, data),

  // 删除任务（监督者）
  deleteTask: (taskId) => api.delete(`/tasks/${taskId}`),

  // 提交任务完成（学生）
  submitTaskCompletion: (taskId, data) => api.post(`/tasks/${taskId}/complete`, data),

  // 审核任务完成（监督者）
  reviewTaskCompletion: (completionId, data) => api.put(`/tasks/completion/${completionId}`, data),

  // 获取我的任务
  getMyTasks: (params) => api.get('/tasks/my-tasks', { params }),

  // 获取固定任务列表（任务广场）
  getFixedTasks: () => api.get('/tasks/fixed'),
  // 获取开放任务列表（教师发布，未被领取）
  getOpenTasks: () => api.get('/tasks/open'),

  // 接受固定任务
  acceptFixedTask: (taskData) => api.post('/tasks/fixed/accept', { taskData }),
  // 领取开放任务
  acceptOpenTask: (taskId) => api.post(`/tasks/open/${taskId}/accept`),

  // 获取学生任务列表（监督者用）
  getStudentTasks: (studentId) => api.get(`/tasks/student/${studentId}`),

  // 获取待审核任务列表（监督者用）
  getPendingReviewTasks: () => api.get('/tasks/completions/pending'),

  // {{ AURA-X: Add - 添加学科进度和周学习统计API方法. Approval: 寸止(ID:1734683400). }}
  // 获取学科进度统计
  getSubjectProgress: () => api.get('/tasks/subject-progress'),

  // 获取周学习统计
  getWeeklyStats: () => api.get('/tasks/weekly-stats')
}
