$ErrorActionPreference = 'Stop'
$base = 'http://localhost:5000/api'

# 1) Register temp student
$stamp = Get-Random -Maximum 1000000
$registerBody = @{ 
  username = "logic_user_$stamp"
  password = "P@ssw0rd!"
  email    = "logic_$<EMAIL>"
} | ConvertTo-Json

$reg = Invoke-RestMethod -Method Post -Uri "$base/auth/register" -Body $registerBody -ContentType 'application/json'
$token = $reg.token
if (-not $token) { throw 'No token from register' }
$headers = @{ Authorization = "Bearer $token" }

# 2) Get fixed tasks
$resp = Invoke-RestMethod -Method Get -Uri "$base/tasks/fixed" -Headers $headers

"COUNT=$($resp.data.Count)"
$ids = $resp.data | ForEach-Object { $_.id }
"HAS_fixed-task-11=$($ids -contains 'fixed-task-11')"
"HAS_fixed-task-12=$($ids -contains 'fixed-task-12')"
"HAS_fixed-task-13=$($ids -contains 'fixed-task-13')"

$sampleDue = $resp.data[0].dueDate
"SAMPLE_DUE=$sampleDue"

# 3) Try accept one logic task by id fixed-task-11 if exists
$logic = $resp.data | Where-Object { $_.id -in @('fixed-task-11','fixed-task-12','fixed-task-13') }
if ($logic.Count -gt 0) {
  $task = $logic[0]
  $acceptBody = @{ taskData = $task } | ConvertTo-Json -Depth 6
  $acc = Invoke-RestMethod -Method Post -Uri "$base/tasks/fixed/accept" -Headers $headers -Body $acceptBody -ContentType 'application/json'
  "ACCEPTED_ID=$($acc.data.task.id)"
  $my = Invoke-RestMethod -Method Get -Uri "$base/tasks/my-tasks" -Headers $headers
  $has = @($my.data | Where-Object { $_.title -eq $task.title }).Count -gt 0
  "MY_HAS_ACCEPTED=$has"
} else {
  "NO_LOGIC_TASKS"
}
