// 预加载脚本
window.addEventListener('DOMContentLoaded', () => {
  // 设置 API 基础 URL 为CloudBase云端后端
  window.API_BASE_URL = 'https://exam-task-system-180072-10-1374117124.sh.run.tcloudbase.com/api';
  
  console.log('预加载脚本已执行，API 地址已设置为:', window.API_BASE_URL);
  
  // 将 API 地址存储到 localStorage
  try {
    localStorage.setItem('API_BASE_URL', window.API_BASE_URL);
  } catch (error) {
    console.error('无法存储 API 地址到 localStorage:', error);
  }
  
  // 向页面注入一个通知，表明这是 Electron 应用
  const notificationDiv = document.createElement('div');
  notificationDiv.style.position = 'fixed';
  notificationDiv.style.bottom = '10px';
  notificationDiv.style.right = '10px';
  notificationDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  notificationDiv.style.color = 'white';
  notificationDiv.style.padding = '8px 12px';
  notificationDiv.style.borderRadius = '4px';
  notificationDiv.style.fontSize = '12px';
  notificationDiv.style.zIndex = '9999';
  notificationDiv.textContent = '考研任务系统桌面版';
  notificationDiv.style.opacity = '0.8';
  
  // 5秒后自动隐藏
  setTimeout(() => {
    notificationDiv.style.opacity = '0';
    notificationDiv.style.transition = 'opacity 1s';
    setTimeout(() => {
      if (notificationDiv.parentNode) {
        notificationDiv.parentNode.removeChild(notificationDiv);
      }
    }, 1000);
  }, 5000);
  
  document.body.appendChild(notificationDiv);
});