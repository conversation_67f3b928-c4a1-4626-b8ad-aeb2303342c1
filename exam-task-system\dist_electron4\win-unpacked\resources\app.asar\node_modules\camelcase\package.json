{"name": "camelcase", "version": "5.3.1", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": "sindresorhus/camelcase", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}}