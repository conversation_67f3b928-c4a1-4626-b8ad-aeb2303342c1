const express = require('express');
const router = express.Router();
const taskController = require('../controllers/taskController');
const { authenticate, isSupervisor } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticate);

// 获取任务列表
router.get('/', taskController.getTasks);

// 获取我的任务（学生用）
router.get('/my-tasks', taskController.getMyTasks);

// 获取固定任务列表（任务广场）
router.get('/fixed', taskController.getFixedTasks);
// 获取监督者开放任务列表（任务广场）
router.get('/open', taskController.getOpenTasks);

// 接受固定任务
router.post('/fixed/accept', taskController.acceptFixedTask);
// 领取监督者开放任务
router.post('/open/:id/accept', taskController.acceptOpenTask);

// 获取学生任务列表（监督者用）
router.get('/student/:studentId', taskController.getStudentTasks);

// 清理学生任务数据（开发测试用）
router.delete('/student/:studentId/clear', taskController.clearStudentTasks);

// 重置教师端测试环境（开发测试用）
router.delete('/supervisor/:supervisor_id/reset', taskController.resetSupervisorTestEnvironment);

// 检查数据库状态（开发测试用）
router.get('/supervisor/:supervisor_id/status', taskController.checkDatabaseStatus);

// 重置整个测试环境（开发测试用）
router.delete('/reset-test-environment', taskController.resetTestEnvironment);

// 获取待审核任务列表（监督者用）- 移到权限检查之前
router.get('/completions/pending', taskController.getPendingReviewTaskCompletions);

// 测试接口：获取所有任务完成记录
router.get('/completions/all', taskController.getAllTaskCompletions);

// {{ AURA-X: Add - 添加学科进度和周学习统计路由. Approval: 寸止(ID:1734683400). }}
// 获取学科进度统计
console.log('注册学科进度统计路由: GET /subject-progress');
router.get('/subject-progress', taskController.getSubjectProgress);

// 获取周学习统计
console.log('注册周学习统计路由: GET /weekly-stats');
router.get('/weekly-stats', taskController.getWeeklyStats);

// {{ AURA-X: Add - 添加今日学习时长统计路由. Approval: 寸止(ID:1734684029). }}
// 获取今日学习时长统计
console.log('注册今日学习时长统计路由: GET /today-study-time');
router.get('/today-study-time', taskController.getTodayStudyTime);

// 获取任务详情（放在静态路由之后，避免误匹配）
router.get('/:id', taskController.getTaskById);

// 提交任务完成情况（学生用）
router.post('/:id/complete', taskController.submitTaskCompletion);

// 以下路由需要监督者权限
router.use(isSupervisor);

// 创建新任务
router.post('/', taskController.createTask);

// 更新任务
router.put('/:id', taskController.updateTask);

// 删除任务
router.delete('/:id', taskController.deleteTask);

// 审核任务完成情况
router.put('/completion/:id', taskController.reviewTaskCompletion);

module.exports = router;