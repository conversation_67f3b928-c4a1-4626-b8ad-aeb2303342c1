{"name": "birpc", "type": "module", "version": "2.5.0", "description": "Message based Two-way remote procedure call", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/birpc#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/birpc.git"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@antfu/ni": "^24.3.0", "@types/node": "^22.13.13", "bumpp": "^10.1.0", "eslint": "^9.23.0", "tsx": "^4.19.3", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vite": "^6.2.3", "vitest": "^3.0.9"}}