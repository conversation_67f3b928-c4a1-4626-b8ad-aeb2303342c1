const { Task, User, TaskCompletion, Point } = require('../models');
const config = require('../config/config');

// 创建新任务（仅监督者可用）
const createTask = async (req, res, next) => {
  try {
    // 确保请求者是监督者
    if (req.user.role !== 'supervisor') {
      return res.status(403).json({ message: '权限不足' });
    }
    
    const { title, description, dueDate, points, studentId } = req.body;
    
    // 如果指定了学生，确保该学生存在
    if (studentId) {
      const student = await User.getUserById(studentId);
      
      if (!student || student.role !== 'student') {
        return res.status(404).json({ message: '指定的学生不存在' });
      }
    }
    
    // 创建任务
    const task = await Task.createTask({
      title,
      description,
      dueDate,
      points: points || 10,
      creatorId: req.user.id,
      studentId: studentId || null
    });
    
    res.status(201).json({
      message: '任务创建成功',
      task
    });
  } catch (error) {
    next(error);
  }
};

// 获取任务列表
const getTasks = async (req, res, next) => {
  try {
    const { status, studentId } = req.query;
    const query = {};
    
    // 根据状态筛选
    if (status) {
      query.status = status;
    }
    
    // 根据角色筛选任务
    if (req.user.role === 'student') {
      // 学生只能查看分配给自己的任务
      query.studentId = req.user.id;
      const tasks = await Task.getTasks(query);
      return res.json({ tasks });
    } else if (req.user.role === 'supervisor') {
      // 监督者可以查看自己创建的任务，或者根据学生ID筛选
      if (studentId) {
        query.studentId = studentId;
        const tasks = await Task.getTasks(query);
        return res.json({ tasks });
      } else {
        query.creatorId = req.user.id;
        const tasks = await Task.getTasks(query);
        return res.json({ tasks });
      }
    }
    
    res.status(403).json({ message: '权限不足' });
  } catch (error) {
    next(error);
  }
};

// 获取固定任务列表（任务广场）
const getFixedTasks = async (req, res, next) => {
  try {
    // 获取今天的日期，设置为当天23:59:59（使用本地时间字符串）
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const date = String(today.getDate()).padStart(2, '0');
    const todayDueTime = `${year}-${month}-${date} 23:59:59`;

    // 获取明天的日期，设置为明天23:59:59
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowYear = tomorrow.getFullYear();
    const tomorrowMonth = String(tomorrow.getMonth() + 1).padStart(2, '0');
    const tomorrowDate = String(tomorrow.getDate()).padStart(2, '0');
    const tomorrowDueTime = `${tomorrowYear}-${tomorrowMonth}-${tomorrowDate} 23:59:59`;

    // 获取本周末的日期
    const weekEnd = new Date(today);
    const dayOfWeek = weekEnd.getDay(); // 0是周日，1-6是周一到周六
    const daysUntilWeekend = dayOfWeek === 0 ? 0 : 7 - dayOfWeek; // 计算到周末还有几天
    weekEnd.setDate(weekEnd.getDate() + daysUntilWeekend);
    const weekEndYear = weekEnd.getFullYear();
    const weekEndMonth = String(weekEnd.getMonth() + 1).padStart(2, '0');
    const weekEndDate = String(weekEnd.getDate()).padStart(2, '0');
    const weekEndDueTime = `${weekEndYear}-${weekEndMonth}-${weekEndDate} 23:59:59`;
    
    // 定义固定任务列表
    const fixedTasks = [
      {
        id: "fixed-task-1",
        title: "专业课背诵",
        description: "背诵专业课重要知识点和核心概念，每天至少1小时",
        type: "daily",
        subject: "专业课",
        difficulty: 2,
        dueDate: todayDueTime,
        points: 20,
        target: "1小时",
        requirements: ["记录背诵内容", "整理知识点", "完成自测"],
        completionCriteria: "提交背诵内容记录和自测结果",
        isFixed: true
      },
      {
        id: "fixed-task-10",
        title: "专业课综合复习",
        description: "全面复习专业课内容，构建知识体系",
        type: "daily",
        subject: "专业课",
        difficulty: 3,
        dueDate: todayDueTime,
        points: 40,
        target: "1.5-3小时",
        requirements: ["复习重点章节", "整理知识框架", "解决疑难问题"],
        completionCriteria: "提交复习总结和知识体系图",
        isFixed: true
      },
      {
        id: "fixed-task-2",
        title: "数学网课学习",
        description: "观看数学网课视频，做好笔记，理解关键概念",
        type: "daily",
        subject: "数学",
        difficulty: 1.5,
        dueDate: todayDueTime,
        points: 20,
        target: "1.5小时",
        requirements: ["完整观看视频", "记录笔记", "总结重点"],
        completionCriteria: "提交学习笔记和重点总结",
        isFixed: true
      },
      {
        id: "fixed-task-3",
        title: "数学网课课后练习",
        description: "完成数学网课的课后练习题，巩固所学知识",
        type: "daily",
        subject: "数学",
        difficulty: 3,
        dueDate: todayDueTime,
        points: 20,
        target: "1小时",
        requirements: ["完成课后练习", "理解解题思路", "总结解题方法"],
        completionCriteria: "提交练习完成记录和解题心得",
        isFixed: true
      },
      {
        id: "fixed-task-4",
        title: "数学知识点复习",
        description: "复习之前学过的数学知识点，确保理解和记忆",
        type: "daily",
        subject: "数学",
        difficulty: 2,
        dueDate: todayDueTime,
        points: 20,
        target: "1小时",
        requirements: ["回顾笔记", "整理知识框架", "解决疑难点"],
        completionCriteria: "提交复习总结和疑难点解决方案",
        isFixed: true
      },
      {
        id: "fixed-task-5",
        title: "英语单词背诵（巩固）",
        description: "背诵英语单词，包括拼写、发音和用法",
        type: "daily",
        subject: "英语",
        difficulty: 1,
        dueDate: todayDueTime,
        points: 15,
        target: "100个单词",
        requirements: ["记忆单词拼写", "理解单词含义", "掌握单词用法"],
        completionCriteria: "通过单词测试，正确率达到90%以上",
        isFixed: true
      },
      {
        id: "fixed-task-6",
        title: "英语题型练习",
        description: "练习英语各种题型，提高应试能力",
        type: "daily",
        subject: "英语",
        difficulty: 2,
        dueDate: todayDueTime,
        points: 20,
        target: "3篇文章",
        requirements: ["完成阅读理解", "练习完形填空", "掌握解题技巧"],
        completionCriteria: "提交题型练习完成记录和错题分析",
        isFixed: true
      },
      {
        id: "fixed-task-7",
        title: "政治网课学习",
        description: "观看政治网课视频，理解重要概念和理论",
        type: "daily",
        subject: "政治",
        difficulty: 2,
        dueDate: todayDueTime,
        points: 20,
        target: "1.5小时",
        requirements: ["完整观看视频", "记录笔记", "总结重点"],
        completionCriteria: "提交学习笔记和重点总结",
        isFixed: true
      },
      {
        id: "fixed-task-8",
        title: "政治知识点复习",
        description: "复习政治重要知识点和理论框架",
        type: "daily",
        subject: "政治",
        difficulty: 2,
        dueDate: todayDueTime,
        points: 20,
        target: "1小时",
        requirements: ["回顾笔记", "整理知识框架", "理解核心概念"],
        completionCriteria: "提交复习总结和知识框架图",
        isFixed: true
      },
      {
        id: "fixed-task-9",
        title: "政治题型训练",
        description: "练习政治各种题型，提高应试能力",
        type: "daily",
        subject: "政治",
        difficulty: 3,
        dueDate: todayDueTime,
        points: 20,
        target: "45分钟-1.5小时",
        requirements: ["练习选择题", "练习分析题", "掌握答题技巧"],
        completionCriteria: "提交题型练习完成记录和错题总结",
        isFixed: true
      }
      ,
      {
        id: "fixed-task-11",
        title: "逻辑网课学习",
        description: "观看逻辑网课视频，理解形式逻辑与论证方法",
        type: "daily",
        subject: "逻辑",
        difficulty: 2,
        dueDate: todayDueTime,
        points: 20,
        target: "1-1.5小时",
        requirements: ["完整观看视频", "记录关键概念", "整理例题思路"],
        completionCriteria: "提交学习笔记与关键概念总结",
        isFixed: true
      },
      {
        id: "fixed-task-12",
        title: "逻辑作业",
        description: "完成逻辑课程配套练习题，巩固推理规则",
        type: "daily",
        subject: "逻辑",
        difficulty: 3,
        dueDate: todayDueTime,
        points: 20,
        target: "1小时/2套题",
        requirements: ["完成练习", "标注难点", "归纳题型方法"],
        completionCriteria: "提交作业完成记录和错题分析",
        isFixed: true
      },
      {
        id: "fixed-task-13",
        title: "逻辑知识点复习",
        description: "复习逻辑关键知识点，梳理解题模板",
        type: "daily",
        subject: "逻辑",
        difficulty: 2,
        dueDate: todayDueTime,
        points: 20,
        target: "45分钟-1.5小时",
        requirements: ["回顾笔记", "整理知识树", "总结易错点"],
        completionCriteria: "提交复习提纲与知识框架图",
        isFixed: true
      }
    ];
    
    // 获取用户已接受的任务ID列表
    const userTasks = await Task.getStudentTasks(req.user.id);
    const acceptedTaskIds = userTasks.map(task => task.id);
    
    // 过滤掉用户已接受的固定任务（通过标题匹配）
    const availableTasks = fixedTasks.filter(fixedTask => 
      !userTasks.some(userTask => userTask.title === fixedTask.title && 
                                 new Date(userTask.dueDate).toDateString() === new Date(fixedTask.dueDate).toDateString())
    );
    
    res.json({
      message: '获取成功',
      data: availableTasks
    });
  } catch (error) {
    next(error);
  }
};

// 获取监督者开放任务（未分配给学生、可领取的）
const getOpenTasks = async (req, res, next) => {
  try {
    // 仅返回未分配 studentId 的任务（监督者创建，公开给学生领取）
    const now = new Date();
    const open = await Task.getTasks({ studentId: null });
    // 可选：过滤掉过期任务（有截止时间且已过期）
    const data = open.filter(t => !t.deadline || new Date(t.deadline) >= now)
      .map(t => ({ ...t, isFixed: false, isOpen: true }));

    res.json({ message: '获取开放任务成功', data });
  } catch (error) {
    next(error);
  }
};

// 领取监督者开放任务（把任务分配给当前学生）
const acceptOpenTask = async (req, res, next) => {
  try {
    const { id } = req.params;

    // 必须是学生
    if (req.user.role !== 'student') {
      return res.status(403).json({ message: '权限不足' });
    }

    const task = await Task.getTaskById(id);
    if (!task) return res.status(404).json({ message: '任务不存在' });
    if (task.studentId) return res.status(400).json({ message: '任务已被其他学生领取' });

    // 领取：把任务分配给当前学生并置为进行中
    const updated = await Task.updateTask(id, {
      studentId: req.user.id,
      status: 'in_progress',
      acceptedDate: new Date().toISOString()
    });

    res.json({ message: '任务领取成功', data: { task: updated } });
  } catch (error) {
    next(error);
  }
};

// 获取任务详情
const getTaskById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const task = await Task.getTaskById(id);
    
    if (!task) {
      return res.status(404).json({ message: '任务不存在' });
    }
    
    // 检查权限：学生只能查看分配给自己的任务
    if (req.user.role === 'student' && task.studentId !== req.user.id) {
      return res.status(403).json({ message: '权限不足' });
    }
    
    // 获取任务的完成记录
    const completions = await TaskCompletion.getTaskCompletionsByTaskId(id);
    
    // 获取创建者和学生信息
    let creator = null;
    let student = null;
    
    if (task.creatorId) {
      creator = await User.getUserById(task.creatorId);
    }
    
    if (task.studentId) {
      student = await User.getUserById(task.studentId);
    }
    
    res.json({
      task: {
        ...task,
        completions,
        creator,
        student
      }
    });
  } catch (error) {
    next(error);
  }
};

// 更新任务
const updateTask = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { title, description, dueDate, status, points, studentId } = req.body;
    
    const task = await Task.getTaskById(id);
    
    if (!task) {
      return res.status(404).json({ message: '任务不存在' });
    }
    
    // 检查权限：只有创建者（监督者）可以更新任务
    if (task.creatorId !== req.user.id) {
      return res.status(403).json({ message: '权限不足' });
    }
    
    // 如果指定了学生，确保该学生存在
    if (studentId && studentId !== task.studentId) {
      const student = await User.getUserById(studentId);
      
      if (!student || student.role !== 'student') {
        return res.status(404).json({ message: '指定的学生不存在' });
      }
    }
    
    // 更新任务
    const updatedTask = await Task.updateTask(id, {
      title: title || task.title,
      description: description !== undefined ? description : task.description,
      dueDate: dueDate || task.dueDate,
      status: status || task.status,
      points: points || task.points,
      studentId: studentId !== undefined ? studentId : task.studentId
    });
    
    res.json({
      message: '任务更新成功',
      task: updatedTask
    });
  } catch (error) {
    next(error);
  }
};

// 删除任务
const deleteTask = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const task = await Task.getTaskById(id);
    
    if (!task) {
      return res.status(404).json({ message: '任务不存在' });
    }
    
    // 检查权限：只有创建者（监督者）可以删除任务
    if (task.creatorId !== req.user.id) {
      return res.status(403).json({ message: '权限不足' });
    }
    
    // 删除任务
    await Task.deleteTask(id);
    
    res.json({ message: '任务删除成功' });
  } catch (error) {
    next(error);
  }
};

// 提交任务完成情况（学生用）
const submitTaskCompletion = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { evidence, timeSpent, selfRating, reflection } = req.body;

    console.log('=== 任务提交调试 ===');
    console.log('任务ID:', id);
    console.log('用户ID:', req.user?.id);
    console.log('用户角色:', req.user?.role);
    console.log('提交数据:', req.body);

    const task = await Task.getTaskById(id);
    console.log('找到的任务:', task ? { id: task._id, title: task.title, studentId: task.studentId, status: task.status } : '未找到任务');

    if (!task) {
      return res.status(404).json({ message: '任务不存在' });
    }

    // {{ AURA-X: Modify - 允许 in_progress 首次提交与 rejected 重新提交. Approval: 寸止(ID:1734684016). }}
    // 检查任务状态：允许 pending/in_progress（首次提交）与 rejected（被拒后重提）
    if (task.status !== 'pending' && task.status !== 'rejected' && task.status !== 'in_progress') {
      return res.status(400).json({
        message: `任务当前状态为 ${task.status}，无法提交。仅待处理、进行中或被拒绝的任务允许提交/重提。`
      });
    }
    
    // 检查权限：只有被分配的学生可以提交任务完成情况
    console.log('权限检查:', { taskStudentId: task.studentId, currentUserId: req.user.id, match: task.studentId === req.user.id });
    if (task.studentId !== req.user.id) {
      console.log('权限不足，任务不属于当前用户');
      return res.status(403).json({ message: '权限不足' });
    }
    
    // 检查是否已有待审核/已通过记录；submitted 状态不再阻止首次提交
    const hasCompletion = await TaskCompletion.hasTaskCompletion(id, req.user.id);
    console.log('完成状态检查:', { hasCompletion, taskStatus: task.status });

    if (hasCompletion || task.status === 'completed') {
      console.log('任务已提交或已完成:', { hasCompletion, status: task.status });
      return res.status(400).json({ message: '任务已提交或已完成' });
    }

    console.log('开始提交任务:', { taskId: id, studentId: req.user.id });

    // {{ AURA-X: Modify - 重新提交时清理旧的完成记录. Approval: 寸止(ID:1734684016). }}
    // 如果是重新提交（任务状态为rejected或submitted），先删除旧的完成记录
    if (task.status === 'rejected' || task.status === 'submitted') {
      console.log('检测到重新提交，清理旧的完成记录...');
      try {
        await TaskCompletion.deleteCompletionsByTask(id);
        console.log('旧的完成记录已清理');
      } catch (error) {
        console.warn('清理旧完成记录失败，继续提交:', error.message);
      }
    }

    // 创建任务完成记录
    console.log('创建任务完成记录...');
    const taskCompletion = await TaskCompletion.createTaskCompletion({
      taskId: id,
      studentId: req.user.id,
      timeSpent: typeof timeSpent === 'number' ? timeSpent : 0,
      evidence,
      selfRating: typeof selfRating === 'number' ? selfRating : null,
      reflection: reflection || '',
      status: 'pending_review'
    });
    console.log('任务完成记录创建成功:', taskCompletion);

    // 更新任务状态为已提交，并设置提交时间，清除拒绝相关字段
    console.log('更新任务状态...');
    await Task.updateTask(id, {
      status: 'submitted',
      submittedDate: new Date().toISOString(),
      rejectionReason: null,
      rejectedDate: null
    });
    console.log('任务状态更新成功');

    console.log('=== 任务提交成功 ===');
    res.status(201).json({
      message: '任务提交成功，等待审核',
      taskCompletion
    });
  } catch (error) {
    next(error);
  }
};

// {{ AURA-X: Add - 添加获取我的任务方法. Approval: 寸止(ID:1734684000). }}
// 获取我的任务（学生用）
const getMyTasks = async (req, res, next) => {
  try {
    // 确保请求者是学生
    if (req.user.role !== 'student') {
      return res.status(403).json({ message: '权限不足' });
    }

    // 获取该学生的所有任务
    const tasks = await Task.getTasks({
      studentId: req.user.id
    });

    console.log('=== getMyTasks 调试 ===');
    console.log('学生ID:', req.user.id);
    console.log('任务数量:', tasks.length);
    tasks.forEach((task, index) => {
      console.log(`任务${index + 1}: ${task.title}, 状态: ${task.status}`);
    });

    // 统一口径：合并最近一次完成记录的关键信息（自评/反思/证据/用时）
    const completions = await TaskCompletion.getStudentTaskCompletions(req.user.id);
    const latestByTask = {};
    completions.forEach(c => {
      const key = c.taskId;
      const ts = c.reviewDate ? new Date(c.reviewDate).getTime() : (c.completionDate ? new Date(c.completionDate).getTime() : 0);
      const prev = latestByTask[key];
      if (!prev || ts > prev._ts) {
        latestByTask[key] = { ...c, _ts: ts };
      }
    });

    const enriched = tasks.map(t => {
      const lc = latestByTask[t.id] || latestByTask[t._id];
      if (!lc) return t;

      // 基础字段回填
      const base = {
        ...t,
        selfRating: t.selfRating ?? lc.selfRating ?? null,
        reflection: t.reflection ?? lc.reflection ?? '',
        completionEvidence: t.completionEvidence ?? lc.evidence ?? '',
        timeSpent: (typeof t.timeSpent === 'number') ? t.timeSpent : (lc.timeSpent ?? null),
        completedAt: t.completedAt || (lc.reviewDate || lc.completionDate || null)
      };

      // 兼容性处理：若最近一次完成记录是被拒绝，且主任务并非已完成，则派生为 rejected，补充原因与时间
      if ((lc.status === 'rejected') && base.status !== 'completed') {
        return {
          ...base,
          status: 'rejected',
          rejectionReason: base.rejectionReason || lc.feedback || '任务不符合要求',
          rejectedDate: base.rejectedDate || lc.reviewDate || lc.completionDate || new Date().toISOString()
        };
      }

      return base;
    });

    res.json({
      message: '获取我的任务成功',
      data: enriched
    });
  } catch (error) {
    console.error('获取我的任务失败:', error);
    next(error);
  }
};

// 接受固定任务（学生用）
const acceptFixedTask = async (req, res, next) => {
  try {
    const { taskData } = req.body;
    
    if (!taskData) {
      return res.status(400).json({ message: '任务数据不完整' });
    }
    
    // 确保请求者是学生
    if (req.user.role !== 'student') {
      return res.status(403).json({ message: '权限不足' });
    }
    
    // 检查学生今天已接受的任务数量
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    const todayTasks = await Task.getTasks({
      studentId: req.user.id,
      createdAt: { $gte: startOfDay, $lte: endOfDay }
    });
    
    // 检查是否已经接受了相同的任务
    const existingTask = todayTasks.find(task => 
      task.title === taskData.title && 
      new Date(task.dueDate).toDateString() === new Date(taskData.dueDate).toDateString()
    );
    
    if (existingTask) {
      return res.status(400).json({ message: '您已经接受了这个任务' });
    }
    
    // 创建任务
    const task = await Task.createTask({
      title: taskData.title,
      description: taskData.description,
      dueDate: taskData.dueDate,
      deadline: taskData.deadline, // 添加deadline字段
      points: taskData.points,
      status: 'in_progress', // 设置为进行中状态
      acceptedDate: new Date().toISOString(), // 添加接受日期
      creatorId: null, // 系统自动创建
      studentId: req.user.id,
      subject: taskData.subject,
      difficulty: taskData.difficulty,
      type: taskData.type,
      target: taskData.target,
      requirements: taskData.requirements,
      completionCriteria: taskData.completionCriteria,
      isFixed: true
    });
    
    res.status(201).json({
      message: '任务领取成功',
      data: {
        task
      }
    });
  } catch (error) {
    next(error);
  }
};

// 审核任务完成情况（监督者用）
const reviewTaskCompletion = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, feedback, pointsAwarded } = req.body;
    
    // 确保请求者是监督者
    if (req.user.role !== 'supervisor') {
      return res.status(403).json({ message: '权限不足' });
    }
    
    const taskCompletion = await TaskCompletion.getTaskCompletionById(id);
    
    if (!taskCompletion) {
      return res.status(404).json({ message: '任务完成记录不存在' });
    }
    
    // 检查任务是否已经审核
    if (taskCompletion.status !== 'pending_review') {
      return res.status(400).json({ message: '任务已经审核过' });
    }
    
    // 获取任务信息
    const task = await Task.getTaskById(taskCompletion.taskId);
    
    if (!task) {
      return res.status(404).json({ message: '任务不存在' });
    }
    
    // 更新任务完成记录
    const updatedCompletion = await TaskCompletion.reviewTaskCompletion(
      id,
      status,
      feedback,
      req.user.id,
      pointsAwarded || task.points
    );
    
    // 如果审核通过，更新任务状态并奖励积分，并把关键信息回填到任务上（统一前端口径）
    if (status === 'approved') {
      // 更新任务状态
      console.log('=== 审核通过，更新任务状态 ===');
      console.log('任务ID:', taskCompletion.taskId);
      console.log('更新前任务状态:', task.status);

      await Task.updateTaskStatus(taskCompletion.taskId, 'completed');
      // 回填最近一次完成记录的关键信息，便于前端直接展示
      await Task.updateTask(taskCompletion.taskId, {
        selfRating: updatedCompletion?.selfRating ?? taskCompletion.selfRating ?? null,
        reflection: updatedCompletion?.reflection ?? taskCompletion.reflection ?? '',
        completionEvidence: updatedCompletion?.evidence ?? taskCompletion.evidence ?? '',
        timeSpent: updatedCompletion?.timeSpent ?? taskCompletion.timeSpent ?? 0,
        completedAt: new Date().toISOString()
      });
      console.log('任务状态已更新为: completed');
      
      // 奖励积分
      const pointsToAward = pointsAwarded || task.points;
      await User.updateUserPoints(taskCompletion.studentId, pointsToAward);
      
      // 记录积分变动
      await Point.recordTaskCompletionPoints(
        taskCompletion.studentId,
        pointsToAward,
        taskCompletion.taskId,
        task.title
      );
    } else if (status === 'rejected') {
      // {{ AURA-X: Modify - 修改拒绝逻辑，保持rejected状态并保存拒绝原因. Approval: 寸止(ID:1734684009). }}
      // 如果拒绝，将任务状态设为rejected，并保存拒绝原因
      await Task.updateTask(taskCompletion.taskId, {
        status: 'rejected',
        rejectionReason: feedback || '任务不符合要求',
        rejectedDate: new Date().toISOString()
      });
    }
    
    res.json({
      message: '任务审核完成',
      taskCompletion: updatedCompletion
    });
  } catch (error) {
    next(error);
  }
};

// 获取学生任务列表（监督者用）
const getStudentTasks = async (req, res, next) => {
  try {
    const { studentId } = req.params;

    if (!studentId) {
      return res.status(400).json({ message: '学生ID不能为空' });
    }

    // 获取学生的所有任务
    const tasks = await Task.getStudentTasks(studentId);

    // 获取任务完成记录，添加学习时长信息
    const TaskCompletion = require('../models/TaskCompletion');
    const tasksWithStudyTime = await Promise.all(tasks.map(async (task) => {
      try {
        const completions = await TaskCompletion.getTaskCompletionsByTaskId(task._id);
        const completion = completions.find(c => c.studentId === studentId);

        return {
          ...task,
          study_time: completion ? completion.timeSpent || 0 : 0,
          completion_status: completion ? completion.status : 'pending',
          completion_id: completion ? completion._id : null
        };
      } catch (error) {
        console.error('获取任务完成记录失败:', error);
        return {
          ...task,
          study_time: 0,
          completion_status: 'pending',
          completion_id: null
        };
      }
    }));

    res.json({
      message: '获取成功',
      data: tasksWithStudyTime
    });
  } catch (error) {
    console.error('获取学生任务失败:', error);
    next(error);
  }
};

// 清理学生任务数据（开发测试用）
const clearStudentTasks = async (req, res, next) => {
  try {
    const { studentId } = req.params;

    if (!studentId) {
      return res.status(400).json({ message: '学生ID不能为空' });
    }

    // 删除学生的所有任务
    const deletedTasks = await Task.deleteTasksByStudent(studentId);

    // 删除学生的任务完成记录
    const deletedCompletions = await TaskCompletion.deleteCompletionsByStudent(studentId);

    // 删除学生的积分记录
    const deletedPoints = await Point.deletePointsByUser(studentId);

    console.log(`清理学生 ${studentId} 的数据:`, {
      deletedTasks,
      deletedCompletions,
      deletedPoints
    });

    res.json({
      message: '清理成功',
      data: {
        deletedTasks,
        deletedCompletions,
        deletedPoints
      }
    });
  } catch (error) {
    console.error('清理学生任务失败:', error);
    next(error);
  }
};

// {{ AURA-X: Add - 添加教师端测试环境重置功能. Approval: 寸止(ID:1734683400). }}
// 重置教师端测试环境（开发测试用）
const resetSupervisorTestEnvironment = async (req, res, next) => {
  try {
    const { supervisor_id } = req.params;

    if (!supervisor_id) {
      return res.status(400).json({ message: '教师ID不能为空' });
    }

    const Binding = require('../models/Binding');
    const User = require('../models/User');

    // 获取该教师的所有绑定关系（包括inactive）
    const students = await Binding.getAllBindingsBySupervisor(supervisor_id);
    let deletedBindings = 0;
    let deletedTasks = 0;
    let deletedCompletions = 0;
    let deletedPoints = 0;
    let deletedStudents = 0;

    // 处理每个学生
    for (const studentBinding of students) {
      if (studentBinding.student && studentBinding.student.id) {
        const studentId = studentBinding.student.id;

        // 使用现有的清理方法
        try {
          // 删除学生的所有任务
          const taskResult = await Task.deleteTasksByStudent(studentId);
          deletedTasks += taskResult || 0;

          // 删除学生的任务完成记录
          const completionResult = await TaskCompletion.deleteCompletionsByStudent(studentId);
          deletedCompletions += completionResult || 0;

          // 删除学生的积分记录
          const pointResult = await Point.deletePointsByUser(studentId);
          deletedPoints += pointResult || 0;

          // 删除学生用户（如果不是管理员）
          const student = await User.getUserById(studentId);
          if (student && student.role !== 'admin') {
            await User.deleteUser(studentId);
            deletedStudents++;
          }
        } catch (error) {
          console.error(`清理学生 ${studentId} 数据失败:`, error);
        }
      }

      // 删除绑定关系
      try {
        await Binding.deleteBinding(studentBinding.binding_id);
        deletedBindings++;
      } catch (error) {
        console.error(`删除绑定关系 ${studentBinding.binding_id} 失败:`, error);
      }
    }

    // 验证删除结果 - 检查是否还有残留数据
    const remainingStudents = await Binding.getStudentsBySupervisor(supervisor_id);
    const remainingBindings = await Binding.getAllBindingsBySupervisor(supervisor_id);

    console.log(`重置教师 ${supervisor_id} 的测试环境完成:`, {
      deletedTasks,
      deletedCompletions,
      deletedPoints,
      deletedBindings,
      deletedStudents,
      remainingActiveStudents: remainingStudents.length,
      remainingAllBindings: remainingBindings.length
    });

    res.json({
      message: '教师端测试环境重置成功',
      data: {
        supervisor_id,
        deletedTasks,
        deletedCompletions,
        deletedPoints,
        deletedBindings,
        deletedStudents
      }
    });
  } catch (error) {
    console.error('重置教师端测试环境失败:', error);
    next(error);
  }
};

// 重置整个测试环境（开发测试用）
const resetTestEnvironment = async (req, res, next) => {
  try {
    const Binding = require('../models/Binding');
    const User = require('../models/User');

    // 删除所有任务
    const allTasks = await Task.getTasks();
    let deletedTasks = 0;
    for (const task of allTasks) {
      await Task.deleteTask(task._id);
      deletedTasks++;
    }

    // 删除所有任务完成记录
    const allCompletions = await TaskCompletion.getAllTaskCompletions();
    let deletedCompletions = 0;
    for (const completion of allCompletions) {
      await TaskCompletion.deleteTaskCompletion(completion._id);
      deletedCompletions++;
    }

    // 删除所有积分记录
    const allPoints = await Point.getAllPoints();
    let deletedPoints = 0;
    for (const point of allPoints) {
      await Point.deletePointRecord(point._id);
      deletedPoints++;
    }

    // 删除所有绑定关系
    const allBindings = await Binding.getAllBindings();
    let deletedBindings = 0;
    for (const binding of allBindings) {
      await Binding.deleteBinding(binding._id);
      deletedBindings++;
    }

    // 删除测试用户（保留管理员）
    const allUsers = await User.getAllUsers();
    let deletedUsers = 0;
    for (const user of allUsers) {
      if (user.role !== 'admin') {
        await User.deleteUser(user._id);
        deletedUsers++;
      }
    }

    console.log('重置测试环境完成:', {
      deletedTasks,
      deletedCompletions,
      deletedPoints,
      deletedBindings,
      deletedUsers
    });

    res.json({
      message: '测试环境重置成功',
      data: {
        deletedTasks,
        deletedCompletions,
        deletedPoints,
        deletedBindings,
        deletedUsers
      }
    });
  } catch (error) {
    console.error('重置测试环境失败:', error);
    next(error);
  }
};

// {{ AURA-X: Add - 添加数据库状态检查API. Approval: 寸止(ID:1734683400). }}
// 检查数据库状态（开发测试用）
const checkDatabaseStatus = async (req, res, next) => {
  try {
    const { supervisor_id } = req.params;

    const Binding = require('../models/Binding');
    const User = require('../models/User');

    // 检查绑定关系
    const activeBindings = await Binding.getStudentsBySupervisor(supervisor_id);
    const allBindings = await Binding.getAllBindingsBySupervisor(supervisor_id);

    // 检查用户
    const allUsers = await User.getAllUsers();
    const students = allUsers.filter(user => user.role === 'student');

    // 检查任务
    const allTasks = await Task.getTasks();
    const supervisorTasks = allTasks.filter(task => task.creatorId === supervisor_id);

    // 检查完成记录
    const TaskCompletion = require('../models/TaskCompletion');
    const allCompletions = await TaskCompletion.getAllTaskCompletions();

    // 检查积分记录
    const Point = require('../models/Point');
    const allPoints = await Point.getAllPoints();

    const status = {
      supervisor_id,
      activeBindings: activeBindings.length,
      allBindings: allBindings.length,
      totalUsers: allUsers.length,
      studentUsers: students.length,
      totalTasks: allTasks.length,
      supervisorTasks: supervisorTasks.length,
      totalCompletions: allCompletions.length,
      totalPoints: allPoints.length,
      timestamp: new Date()
    };

    console.log('数据库状态检查:', status);

    res.json({
      message: '数据库状态检查完成',
      data: status
    });
  } catch (error) {
    console.error('检查数据库状态失败:', error);
    next(error);
  }
};

// 获取待审核任务完成记录（监督者用）
const getPendingReviewTaskCompletions = async (req, res, next) => {
  try {
    // 确保请求者是监督者
    if (req.user.role !== 'supervisor') {
      return res.status(403).json({ message: '权限不足' });
    }

    // 获取待审核的任务完成记录
    const pendingCompletions = await TaskCompletion.getPendingReviewTaskCompletions();

    // 获取相关的任务和学生信息
    const User = require('../models/User');
    const Task = require('../models/Task');

    const enrichedCompletions = await Promise.all(
      pendingCompletions.map(async (completion) => {
        try {
          const [task, student] = await Promise.all([
            Task.getTaskById(completion.taskId),
            User.getUserById(completion.studentId)
          ]);

          return {
            ...completion,
            task: task ? {
              title: task.title,
              description: task.description,
              points: task.points,
              type: task.type
            } : null,
            student: student ? {
              username: student.username,
              email: student.email
            } : null
          };
        } catch (error) {
          console.error('获取任务或学生信息失败:', error);
          return {
            ...completion,
            task: null,
            student: null
          };
        }
      })
    );

    res.json({
      message: '获取待审核任务成功',
      data: enrichedCompletions
    });
  } catch (error) {
    next(error);
  }
};

// 测试接口：获取所有任务完成记录
const getAllTaskCompletions = async (req, res, next) => {
  try {
    console.log('=== 获取所有任务完成记录 ===');

    // 获取所有任务完成记录
    const allCompletions = await TaskCompletion.getAllTaskCompletions();
    console.log('找到的完成记录数量:', allCompletions.length);

    // 获取相关的任务和学生信息
    const User = require('../models/User');
    const Task = require('../models/Task');

    const enrichedCompletions = await Promise.all(
      allCompletions.map(async (completion) => {
        try {
          const [task, student] = await Promise.all([
            Task.getTaskById(completion.taskId),
            User.getUserById(completion.studentId)
          ]);

          return {
            ...completion,
            task: task ? {
              title: task.title,
              description: task.description,
              points: task.points,
              type: task.type
            } : null,
            student: student ? {
              username: student.username,
              email: student.email
            } : null
          };
        } catch (error) {
          console.error('获取任务或学生信息失败:', error);
          return {
            ...completion,
            task: null,
            student: null
          };
        }
      })
    );

    res.json({
      message: '获取所有任务完成记录成功',
      data: enrichedCompletions,
      total: enrichedCompletions.length
    });
  } catch (error) {
    console.error('获取所有任务完成记录失败:', error);
    next(error);
  }
};

// {{ AURA-X: Add - 添加学科进度统计API. Approval: 寸止(ID:1734683400). }}
// 获取学科进度统计
const getSubjectProgress = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // 获取用户的所有任务
    const tasks = await Task.getTasks({ studentId: userId });

    // 按科目分组统计
    const subjectStats = {};

    // 初始化四个主要科目
    ['数学', '英语', '政治', '专业课'].forEach(subject => {
      subjectStats[subject] = {
        name: subject,
        totalTasks: 0,
        completedTasks: 0,
        percentage: 0
      };
    });

    // 统计任务数据
    tasks.forEach(task => {
      const subject = task.subject;
      if (subject && subjectStats[subject]) {
        subjectStats[subject].totalTasks++;

        if (task.status === 'completed') {
          subjectStats[subject].completedTasks++;
        }
      }
    });

    // 计算百分比
    Object.keys(subjectStats).forEach(subject => {
      const stats = subjectStats[subject];
      stats.percentage = stats.totalTasks > 0
        ? Math.round((stats.completedTasks / stats.totalTasks) * 100 * 10) / 10
        : 0;
    });

    // 转换为数组并排序
    const result = Object.values(subjectStats)
      .sort((a, b) => {
        // 首先按照百分比排序
        if (b.percentage !== a.percentage) {
          return b.percentage - a.percentage;
        }
        // 如果百分比相同，按照科目顺序排序
        const order = { '数学': 0, '英语': 1, '政治': 2, '专业课': 3 };
        return order[a.name] - order[b.name];
      });

    res.json({ data: result });
  } catch (error) {
    console.error('获取科目进度失败:', error);
    next(error);
  }
};

// {{ AURA-X: Modify - 周学习统计改为基于完成记录统计时长与积分. Approval: 寸止(ID:1734684001). }}
// 获取周学习统计（基于 TaskCompletion 审核通过记录）
const getWeeklyStats = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // 获取当前日期和一周前的日期
    const now = new Date();
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    // 获取该学生的完成记录（含 timeSpent/pointsAwarded）
    const completions = await TaskCompletion.getStudentTaskCompletions(userId);

    // 仅统计审核通过且在时间范围内的记录
    const approvedInRange = completions.filter(c => {
      if (c.status !== 'approved') return false;
      const dt = c.reviewDate ? new Date(c.reviewDate) : (c.completionDate ? new Date(c.completionDate) : null);
      return dt && dt >= oneWeekAgo && dt <= now;
    });

    const totalCompleted = approvedInRange.length;

    // 学习总时长（小时，1位小数）
    const totalHours = parseFloat((approvedInRange.reduce((sum, c) => sum + (c.timeSpent || 0), 0) / 60).toFixed(1));

    // 总获得积分（以审核记录的积分为准）
    const totalPoints = approvedInRange.reduce((sum, c) => sum + (c.pointsAwarded || 0), 0);

    // 科目分布需要关联任务信息
    const subjectCounts = {};
    for (const c of approvedInRange) {
      try {
        const task = await Task.getTaskById(c.taskId);
        const subject = task?.subject;
        if (subject) {
          subjectCounts[subject] = (subjectCounts[subject] || 0) + 1;
        }
      } catch (e) {
        // 忽略单条失败，继续统计
      }
    }

    const subjectDistribution = Object.entries(subjectCounts)
      .map(([name, count]) => ({
        name,
        count,
        percentage: totalCompleted > 0 ? Math.round((count / totalCompleted) * 100) : 0
      }))
      .sort((a, b) => b.count - a.count);

    // 学习建议
    const allSubjects = ['数学', '英语', '政治', '专业课'];
    const coveredSubjects = new Set(subjectDistribution.map(s => s.name));
    const missingSubjects = allSubjects.filter(s => !coveredSubjects.has(s));
    const averageCount = totalCompleted / Math.max(coveredSubjects.size, 1);
    const lowPerformingSubjects = subjectDistribution
      .filter(s => s.count < averageCount * 0.8)
      .map(s => s.name);
    const needsAttention = [...new Set([...(totalCompleted === 0 ? allSubjects : []), ...missingSubjects, ...lowPerformingSubjects])];

    const result = { totalCompleted, totalHours, totalPoints, subjectDistribution, needsAttention };
    res.json({ data: result });
  } catch (error) {
    console.error('获取周学习统计失败:', error);
    next(error);
  }
};

module.exports = {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  deleteTask,
  submitTaskCompletion,
  reviewTaskCompletion,
  getMyTasks,
  getFixedTasks,
  getOpenTasks,
  acceptOpenTask,
  acceptFixedTask,
  getStudentTasks,
  clearStudentTasks,
  resetSupervisorTestEnvironment,
  resetTestEnvironment,
  checkDatabaseStatus,
  getPendingReviewTaskCompletions,
  getAllTaskCompletions,
  getSubjectProgress,
  getWeeklyStats
};