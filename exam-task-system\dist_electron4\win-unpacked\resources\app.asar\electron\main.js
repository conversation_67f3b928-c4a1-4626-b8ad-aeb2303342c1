const { app, BrowserWindow } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const isDev = !app.isPackaged;

// 保持对窗口对象的全局引用，避免 JavaScript 对象被垃圾回收时窗口关闭
let mainWindow;
let backendProcess;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载前端应用
  if (isDev) {
    // 开发环境 - 连接到 Vite 开发服务器
    mainWindow.loadURL('http://localhost:5173');
    // 打开开发者工具
    mainWindow.webContents.openDevTools();
    console.log('正在开发模式下运行，连接到 Vite 开发服务器...');
  } else {
    // 生产环境 - 加载打包后的文件
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
    console.log('正在生产模式下运行，加载打包后的文件...');
  }

  // 当窗口关闭时调用的方法
  mainWindow.on('closed', function () {
    mainWindow = null;
  });
}

function startBackend() {
  // 只在开发环境启动本地后端
  if (!isDev) {
    console.log('生产环境：跳过本地后端启动，使用CloudBase云端API');
    return;
  }

  console.log('开发环境：正在启动本地后端服务...');

  // 使用子进程启动后端服务
  const backendPath = path.join(__dirname, '../backend/src/server.js');
  console.log(`后端路径: ${backendPath}`);

  // 确保环境变量正确设置
  const env = {
    ...process.env,
    PORT: 5000,
    NODE_ENV: 'development'
  };

  backendProcess = spawn('node', [backendPath], {
    stdio: 'inherit',
    env: env
  });

  backendProcess.on('error', (err) => {
    console.error('启动后端服务失败:', err);
  });

  // 监听退出事件
  backendProcess.on('exit', (code) => {
    console.log(`后端服务已退出，退出码: ${code}`);
    if (code !== 0 && !app.isQuitting) {
      console.log('尝试重启后端服务...');
      startBackend();
    }
  });
}



// 当 Electron 完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  console.log('Electron 应用启动中...');

  if (isDev) {
    // 开发环境：检查前端服务是否已启动
    const http = require('http');
    const req = http.get('http://localhost:5173', (res) => {
      console.log('前端服务已启动，状态码:', res.statusCode);

      // 启动后端服务
      startBackend();

      // 创建窗口
      setTimeout(() => {
        createWindow();
      }, 1500); // 给后端一点时间启动
    });

    req.on('error', (err) => {
      console.error('无法连接到前端服务，请确保前端服务已启动:', err.message);
      console.log('请先运行 npm run dev 启动前端服务');
      app.quit();
    });

    req.end();
  } else {
    // 生产环境：直接加载静态文件
    console.log('生产环境启动，直接加载静态文件');
    createWindow();
  }

  app.on('activate', function () {
    // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，
    // 通常会在应用程序中重新创建一个窗口
    if (mainWindow === null) createWindow();
  });
});

// 当所有窗口关闭时退出应用
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// 标记应用正在退出，防止后端服务自动重启
app.on('will-quit', () => {
  app.isQuitting = true;
});

// 在应用退出前关闭后端服务
app.on('before-quit', () => {
  if (backendProcess) {
    console.log('正在关闭后端服务...');
    backendProcess.kill();
  }
});
