<template>
  <div class="my-tasks-container">
    <!-- 顶部卡片区域 -->
    <div class="header-card">
      <div class="header-content">
        <div class="header-title">
          <h1>我的任务轨迹</h1>
          <p>每一次坚持，都在塑造更好的自己</p>
        </div>
        
        <div class="stats-row">
          <div class="stat-box">
            <div class="stat-icon repair-stone"></div>
            <div class="stat-info">
              <div class="stat-value">{{ repairStones }}</div>
              <div class="stat-label">修复石</div>
            </div>
          </div>
          
          <div class="stat-box">
            <div class="stat-icon completion-rate" :style="{ '--rate': completionRate }"></div>
            <div class="stat-info">
              <div class="stat-value">{{ completionRate }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
          
          <div class="stat-box">
            <div class="stat-icon streak-days"></div>
            <div class="stat-info">
              <div class="stat-value">{{ consecutiveDays }}</div>
              <div class="stat-label">连续天数</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日历区域 -->
    <div class="content-card calendar-card">
      <div class="card-header">
        <h2>任务轨迹日历</h2>
        <div class="month-nav">
          <el-button @click="prevMonth" :icon="ArrowLeft" circle plain class="nav-button" />
          <span class="month-year">{{ currentMonthYear }}</span>
          <el-button @click="nextMonth" :icon="ArrowRight" circle plain class="nav-button" />

        </div>
      </div>
      
      <div class="calendar">
        <div class="weekdays">
          <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
        </div>
        <div class="days">
          <div 
            v-for="day in calendarDays" 
            :key="day.date" 
            class="day" 
            :class="{ 
              'other-month': !day.currentMonth, 
              'today': day.isToday,
              'has-tasks': day.tasks.length > 0,
              'has-completed': day.hasCompleted,
              'has-failed': day.hasFailed,
              'has-crack': day.hasCrack,
              'crack-level-1': day.hasCrack && day.failureRatio <= 0.33,
              'crack-level-2': day.hasCrack && day.failureRatio > 0.33 && day.failureRatio <= 0.66,
              'crack-level-3': day.hasCrack && day.failureRatio > 0.66,
              'selected': isSelectedDay(day.date)
            }"
            @click="selectDay(day)"
          >
            <span class="day-number">{{ day.dayNumber }}</span>
            <div v-if="day.tasks.length > 0" class="task-dots">
              <div class="dots-row top-dots">
                <div 
                  v-for="(task, index) in day.tasks.slice(0, 3)" 
                  :key="'top-'+index" 
                  class="task-dot"
                  :class="getTaskStatusClass(task)"
                ></div>
              </div>
              <div class="dots-row bottom-dots">
                <div 
                  v-for="(task, index) in day.tasks.slice(3, 6)" 
                  :key="'bottom-'+index" 
                  class="task-dot"
                  :class="getTaskStatusClass(task)"
                ></div>
              </div>
              <span v-if="day.tasks.length > 6" class="more-tasks">+{{ day.tasks.length - 6 }}</span>
            </div>
            <div v-if="day.hasCrack" class="crack-overlay"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务详情区域 -->
    <div class="content-card tasks-card">
      <div class="card-header">
        <h2>{{ selectedDate ? formatDate(selectedDate) : '今日任务' }}</h2>
        <!-- {{ AURA-X: Add - 添加今日学习时长统计显示. Approval: 寸止(ID:1734684021). }} -->
        <div class="study-time-display">
          <span class="study-time-label">今日学习</span>
          <span class="study-time-value">{{ todayStudyHours }}小时</span>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="small"
            class="refresh-button"
            @click="refreshTaskData"
            :loading="refreshLoading"
          >
            <el-icon><Refresh /></el-icon>
            刷新状态
          </el-button>
          <el-button
            v-if="hasCrackOnSelectedDay"
            type="danger"
            class="repair-button"
            @click="repairCrack"
            :disabled="repairStones <= 0"
          >
            修复裂痕 <span class="cost">(消耗1颗修复石)</span>
          </el-button>
        </div>
      </div>
      
      <div v-if="tasksForSelectedDay.length > 0" class="modern-task-list">
        <div
          v-for="task in tasksForSelectedDay"
          :key="task.id"
          class="horizontal-task-card"
          :class="getTaskStatusClass(task)"
        >
          <!-- 状态指示器 -->
          <div class="status-indicator" :class="'status-' + task.status"></div>

          <!-- 主要内容区 -->
          <div class="task-main-content">
            <!-- 第一行：标题、标签、积分 -->
            <div class="task-header-row">
              <h3 class="task-title">{{ task.title }}</h3>
              <div class="task-tags-inline">
                <span class="tag tag-type">{{ getTaskTypeLabel(task.type) }}</span>
                <span class="tag tag-subject" :class="'subject-' + getSubjectClass(task.subject)">
                  {{ task.subject }}
                </span>
                <span class="tag tag-status" :class="'status-' + task.status">
                  {{ getTaskStatusLabel(task.status) }}
                </span>
              </div>
              <div class="points-display">{{ task.points }}</div>
            </div>

            <!-- 第二行：信息 -->
            <div class="task-info-row">
              <div class="task-meta-inline">
                <span class="meta-text">截止时间: {{ formatDate(task.deadline) }}</span>
                <span v-if="task.startTime" class="meta-text">开始时间: {{ formatDate(task.startTime) }}</span>
              </div>
            </div>
            
            <!-- 第三行：操作按钮 -->
            <div class="task-actions-row">
              <template v-if="task.status === 'pending'">
                <el-button type="success" @click="startTask(task)" class="horizontal-btn primary-btn">
                  开始
                </el-button>
                <el-button type="danger" @click="abandonTask(task)" class="horizontal-btn danger-btn" plain>
                  放弃
                </el-button>
              </template>

              <template v-else-if="task.status === 'in_progress'">
                <el-button type="primary" @click="completeTask(task)" class="horizontal-btn primary-btn">
                  完成
                </el-button>
                <el-button @click="updateProgress(task)" class="horizontal-btn secondary-btn" plain>
                  更新进度
                </el-button>
                <el-button type="danger" @click="abandonTask(task)" class="horizontal-btn danger-btn" plain>
                  放弃
                </el-button>
              </template>

              <template v-else-if="task.status === 'submitted'">
                <div class="submitted-info-compact">
                  <span class="submitted-icon">📋</span>
                  <span class="submitted-text">已提交</span>
                  <span class="status-text">等待审核</span>
                  <div class="submitted-actions">
                    <el-button @click="viewSubmissionDetails(task)" class="details-btn-compact" size="small" plain>
                      查看提交
                    </el-button>
                    <!-- {{ AURA-X: Add - 添加已提交状态的重新提交按钮. Approval: 寸止(ID:1734684012). }} -->
                    <el-button @click="resubmitSubmittedTask(task)" class="resubmit-btn-compact" size="small" type="warning" plain>
                      重新提交
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- {{ AURA-X: Add - 添加rejected状态显示模板. Approval: 寸止(ID:1734684006). }} -->
              <template v-else-if="task.status === 'rejected'">
                <div class="rejected-info-compact">
                  <span class="rejected-icon">❌</span>
                  <span class="rejected-text">已拒绝</span>
                  <span class="rejection-reason" v-if="task.rejectionReason">{{ task.rejectionReason }}</span>
                  <div class="rejected-actions">
                    <el-button @click="viewRejectionDetails(task)" class="details-btn-compact" size="small" plain>
                      查看详情
                    </el-button>
                    <el-button @click="resubmitTask(task)" class="resubmit-btn-compact" size="small" type="primary">
                      重新提交
                    </el-button>
                  </div>
                </div>
              </template>

              <template v-else-if="task.status === 'completed'">
                <div class="completion-info-compact">
                  <span class="completion-icon">✅</span>
                  <span class="completion-text">任务完成</span>
                  <span class="points-earned">获得积分: +{{ task.points }}</span>
                  <span v-if="task.selfRating" class="completion-rating">
                    自评: {{ '★'.repeat(task.selfRating) }}{{ '☆'.repeat(5 - task.selfRating) }}
                  </span>
                  <el-button @click="viewTaskEvidence(task)" class="details-btn-compact" size="small" plain>
                    查看提交
                  </el-button>
                </div>
              </template>

              <template v-else-if="task.status === 'failed'">
                <div class="failure-compact-card">
                  <!-- 失败信息行 -->
                  <div class="failure-info-row">
                    <div class="failure-badge-compact">
                      <span class="failure-icon-small">💥</span>
                      <span class="failure-text-compact">任务失败</span>
                    </div>
                    <div class="failure-details-compact">
                      <span class="penalty-compact">-{{ Math.round(task.points * 0.5) }}积分</span>
                      <span v-if="task.failedDate" class="time-compact">{{ formatDate(task.failedDate) }}</span>
                    </div>
                  </div>

                  <!-- 操作按钮行 -->
                  <div class="failure-actions-compact">
                    <el-button type="warning" @click="retryTask(task)" class="compact-btn retry-compact" size="small">
                      重新尝试
                    </el-button>
                    <el-button @click="viewFailureDetails(task)" class="compact-btn details-compact" size="small" plain>
                      查看详情
                    </el-button>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="empty-state">
        <div class="empty-illustration">
          <div class="empty-circle"></div>
          <div class="empty-line line-1"></div>
          <div class="empty-line line-2"></div>
          <div class="empty-line line-3"></div>
        </div>
        <p class="empty-text">{{ selectedDate ? '该日期没有任务' : '今日没有任务' }}</p>
        <el-button type="primary" @click="goToTaskSquare" class="empty-button">前往任务广场</el-button>
      </div>
    </div>

    <!-- 对话框部分 -->
    <!-- 完成任务对话框 -->
    <el-dialog
      v-model="completeDialogVisible"
      title="提交任务完成证明"
      width="500px"
      class="custom-dialog completion-dialog"
    >
      <div v-if="selectedTask" class="dialog-content">
        <div class="task-info-header">
          <h3>{{ selectedTask.title }}</h3>
          <div class="task-badges">
            <span class="badge type">{{ getTaskTypeLabel(selectedTask.type) }}</span>
            <span class="badge subject" :class="'subject-' + getSubjectClass(selectedTask.subject)">{{ selectedTask.subject }}</span>
          </div>
        </div>
        
        <el-alert
          title="诚信提示"
          type="warning"
          description="请如实提交完成情况，虚假提交将被系统记录，并可能导致严重的信用惩罚。"
          show-icon
          :closable="false"
          class="integrity-alert"
        />
        
        <el-form :model="completionForm" label-position="top" class="compact-form">
          <el-form-item label="完成情况描述" required>
            <el-input 
              v-model="completionForm.description" 
              type="textarea" 
              rows="3"
              placeholder="请详细描述您的完成情况..."
            />
          </el-form-item>
          
          <div class="form-row">
            <el-form-item label="完成时长 (分钟)" required>
              <el-input-number
                v-model="completionForm.timeSpent"
                :min="15"
                :max="480"
                controls-position="right"
              />
            </el-form-item>
            
            <el-form-item label="自我评价">
              <el-rate v-model="completionForm.selfRating" :max="5" />
            </el-form-item>
          </div>
          
          <!-- 上传证明材料功能暂时隐藏 -->
          <!-- <el-form-item label="上传证明材料">
            <el-upload
              action="#"
              list-type="picture-card"
              :auto-upload="false"
              :limit="3"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
          </el-form-item> -->
          
          <el-checkbox v-model="completionForm.integrityPledge" class="integrity-checkbox">
            我承诺以上信息真实有效，如有虚假将承担相应后果
          </el-checkbox>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="completeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitTaskCompletion" :disabled="!completionForm.integrityPledge">提交</el-button>
      </template>
    </el-dialog>
    
    <!-- 更新进度对话框 -->
    <el-dialog
      v-model="progressDialogVisible"
      title="更新任务进度"
      width="450px"
      class="custom-dialog progress-dialog"
    >
      <div v-if="selectedTask" class="dialog-content">
        <h3 class="dialog-task-title">{{ selectedTask.title }}</h3>
        
        <el-form :model="progressForm" label-position="top" class="compact-form">
          <el-form-item label="当前进度">
            <el-slider
              v-model="progressForm.progress"
              :step="5"
              :marks="{0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%'}"
            />
          </el-form-item>
          
          <el-form-item label="进度说明">
            <el-input 
              v-model="progressForm.note" 
              type="textarea" 
              rows="2"
              placeholder="简要描述当前的进度情况..."
            />
          </el-form-item>
          
          <el-form-item label="今日学习时长 (分钟)">
            <el-input-number 
              v-model="progressForm.timeSpent" 
              :min="1" 
              :max="480"
              controls-position="right"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="progressDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitProgressUpdate">保存进度</el-button>
      </template>
    </el-dialog>
    
    <!-- 任务失败惩罚对话框 -->
    <el-dialog
      v-model="failureDialogVisible"
      title="任务失败惩罚"
      width="450px"
      class="custom-dialog failure-dialog"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="modern-failure-content">
        <!-- 头部图标和标题 -->
        <div class="failure-header">
          <div class="failure-icon-modern">⚠️</div>
          <h3 class="failure-title-modern">确认放弃任务</h3>
          <p class="failure-subtitle">
            您即将放弃任务 <span class="task-name">{{ selectedTask?.title }}</span>
          </p>
        </div>

        <!-- 后果说明 -->
        <div class="consequences-section">
          <h4 class="consequences-title">放弃任务将产生以下后果：</h4>

          <div class="penalty-cards">
            <div class="penalty-card points-penalty">
              <div class="penalty-card-icon">💰</div>
              <div class="penalty-card-content">
                <h5>积分扣除</h5>
                <p class="penalty-value">-{{ selectedTask ? Math.round(selectedTask.points * 0.5) : 0 }} 积分</p>
                <span class="penalty-desc">扣除任务积分的50%</span>
              </div>
            </div>

            <div class="penalty-card crack-penalty">
              <div class="penalty-card-icon">💔</div>
              <div class="penalty-card-content">
                <h5>日历裂痕</h5>
                <p class="penalty-value">永久标记</p>
                <span class="penalty-desc">在日历上留下裂痕标记</span>
              </div>
            </div>

            <div class="penalty-card streak-penalty">
              <div class="penalty-card-icon">📉</div>
              <div class="penalty-card-content">
                <h5>连续天数</h5>
                <p class="penalty-value">重置为 0</p>
                <span class="penalty-desc">连续完成天数归零</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 提示信息 -->
        <div class="failure-tip">
          <div class="tip-icon">💡</div>
          <p>建议：可以尝试调整任务计划或寻求帮助，而不是直接放弃</p>
        </div>
      </div>

      <template #footer>
        <div class="modern-dialog-footer">
          <el-button @click="cancelAbandon" class="cancel-btn">
            取消放弃
          </el-button>
          <el-button type="danger" @click="acknowledgeFailure" class="confirm-abandon-btn">
            确认放弃
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import {
  ArrowLeft, ArrowRight, Refresh
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useStudentStore } from '@/stores/student';
// {{ AURA-X: Add - 添加taskAPI导入以支持今日学习时长统计. Approval: 寸止(ID:1734684031). }}
import * as taskAPI from '@/api/task';

const router = useRouter();
const studentStore = useStudentStore();

// 常量定义
const STORAGE_KEY_TASKS = 'myTasks';
const STORAGE_KEY_STONES = 'repairStones';
const STORAGE_KEY_STREAK = 'consecutiveDays';
const STORAGE_KEY_POINTS = 'userPoints';
const STORAGE_KEY_PURCHASE_HISTORY = 'purchaseHistory';
const STORAGE_KEY_PRODUCTS = 'shopProducts';
const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
const TASK_TYPES = {
  daily: '每日任务',
  weekly: '周常任务',
  special: '专项任务'
};
const TASK_STATUS = {
  pending: '待开始',
  in_progress: '进行中',
  submitted: '已提交',
  completed: '已完成',
  failed: '已失败'
};
const TASK_STATUS_CLASSES = {
  pending: 'pending',
  submitted: 'submitted',
  completed: 'completed',
  failed: 'failed',
  in_progress: 'in-progress'
};


// 状态变量
const repairStones = ref(3);
const userPoints = ref(0);
const consecutiveDays = ref(0);
const selectedDate = ref(null);
const refreshLoading = ref(false);
// {{ AURA-X: Add - 添加今日学习时长统计数据. Approval: 寸止(ID:1734684022). }}
const todayStudyHours = ref('0.0');
// 使用store中的数据，保持响应式
const myTasks = computed(() => studentStore.myTasks);
const currentMonth = ref(new Date().getMonth());
const currentYear = ref(new Date().getFullYear());
const completeDialogVisible = ref(false);
const progressDialogVisible = ref(false);
const failureDialogVisible = ref(false);
const selectedTask = ref(null);
const completionForm = ref({
  description: '',
  timeSpent: 30, // 这个值会在completeTask函数中动态设置
  selfRating: 3,
  reflection: '',
  integrityPledge: false
});
const progressForm = ref({
  progress: 0,
  note: '',
  timeSpent: 0
});

// 计算属性
const currentMonthYear = computed(() => {
  const date = new Date(currentYear.value, currentMonth.value, 1);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' });
});

const calendarDays = computed(() => {
  const days = [];
  const firstDay = new Date(currentYear.value, currentMonth.value, 1);
  const lastDay = new Date(currentYear.value, currentMonth.value + 1, 0);
  
  const prevMonthLastDay = new Date(currentYear.value, currentMonth.value, 0).getDate();
  const firstDayOfWeek = firstDay.getDay();
  
  // 添加上个月的天数
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const day = prevMonthLastDay - i;
    const date = new Date(currentYear.value, currentMonth.value - 1, day);
    days.push(createDayObject(date, false));
  }
  
  // 添加当前月的天数
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const date = new Date(currentYear.value, currentMonth.value, i);
    days.push(createDayObject(date, true));
  }
  
  // 添加下个月的天数
  const remainingDays = 42 - days.length;
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(currentYear.value, currentMonth.value + 1, i);
    days.push(createDayObject(date, false));
  }
  
  return days;
});

const tasksForSelectedDay = computed(() => {
  if (!selectedDate.value) {
    const today = new Date();
    return filterTasksByDate(today);
  }
  return filterTasksByDate(selectedDate.value);
});

const hasCrackOnSelectedDay = computed(() => {
  if (!selectedDate.value) return false;
  const tasksForDay = tasksForSelectedDay.value;
  const hasFailed = tasksForDay.some(task => task.status === 'failed');
  const hasRepaired = tasksForDay.some(task => task.repaired);
  return hasFailed && !hasRepaired;
});

const completionRate = computed(() => {
  if (myTasks.value.length === 0) return 0;
  const completedTasks = myTasks.value.filter(task => task.status === 'completed').length;
  const totalTasks = myTasks.value.length;
  return Math.round((completedTasks / totalTasks) * 100);
});

// 辅助函数
function createDayObject(date, currentMonth) {
  const today = new Date();
  const isToday = date.toDateString() === today.toDateString();
  const tasksForDay = filterTasksByDate(date);
  
  // 计算完成和失败的任务数量
  const completedTasks = tasksForDay.filter(task => task.status === 'completed');
  const failedTasks = tasksForDay.filter(task => task.status === 'failed');
  
  const hasCompleted = completedTasks.length > 0;
  const hasFailed = failedTasks.length > 0;
  
  // 计算裂痕程度 - 根据失败任务的比例
  const failureRatio = tasksForDay.length > 0 ? failedTasks.length / tasksForDay.length : 0;
  const hasCrack = hasFailed && !tasksForDay.some(task => task.repaired);
  
  return {
    date: date,
    dayNumber: date.getDate(),
    currentMonth: currentMonth,
    isToday: isToday,
    tasks: tasksForDay,
    hasCompleted: hasCompleted,
    hasFailed: hasFailed,
    hasCrack: hasCrack,
    failureRatio: failureRatio
  };
}

function filterTasksByDate(date) {
  return myTasks.value.filter(task => {
    // 支持多种日期字段的匹配（接收/截止/完成/拒绝/提交）
    const acceptedDate = task.acceptedDate ? new Date(task.acceptedDate) : null;
    const deadline = task.deadline ? new Date(task.deadline) : null;
    const completedAt = task.completedAt || task.completionDate || task.reviewDate ? new Date(task.completedAt || task.completionDate || task.reviewDate) : null;
    const rejectedDate = task.rejectedDate || task.reviewedDate ? new Date(task.rejectedDate || task.reviewedDate) : null;
    const submittedDate = task.submittedDate ? new Date(task.submittedDate) : null;
    const targetDate = date.toDateString();

    if (acceptedDate && acceptedDate.toDateString() === targetDate) return true;
    if (deadline && deadline.toDateString() === targetDate) return true;
    if (completedAt && completedAt.toDateString() === targetDate) return true;
    if (rejectedDate && rejectedDate.toDateString() === targetDate) return true;
    if (submittedDate && submittedDate.toDateString() === targetDate) return true;

    return false;
  });
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return 'N/A';
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

const getTaskTypeLabel = (type) => {
  return TASK_TYPES[type] || '未知类型';
};

const getTaskStatusLabel = (status) => {
  return TASK_STATUS[status] || '未知状态';
};

const getTaskStatusClass = (task) => {
  return TASK_STATUS_CLASSES[task.status] || '';
};

const getSubjectClass = (subject) => {
  const subjectMap = {
    '数学': 'math',
    '英语': 'english',
    '政治': 'politics',
    '专业课': 'major'
  };
  return subjectMap[subject] || 'other';
};



const isSelectedDay = (date) => {
  if (!selectedDate.value) return false;
  return date.toDateString() === selectedDate.value.toDateString();
};

// 事件处理函数
const selectDay = (day) => {
  selectedDate.value = new Date(day.date);
};

const prevMonth = () => {
  if (currentMonth.value === 0) {
    currentMonth.value = 11;
    currentYear.value--;
  } else {
    currentMonth.value--;
  }
};

const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentMonth.value = 0;
    currentYear.value++;
  } else {
    currentMonth.value++;
  }
};

// 开始任务
const startTask = async (task) => {
  try {
    // 更新任务状态为进行中
    const taskIndex = myTasks.value.findIndex(t => t.id === task.id);
    if (taskIndex !== -1) {
      myTasks.value[taskIndex].status = 'in_progress';
      myTasks.value[taskIndex].startTime = new Date().toISOString();

      // 保存到本地存储（临时）
      localStorage.setItem('myTasks', JSON.stringify(myTasks.value));

      ElMessage.success('任务已开始！');
    }
  } catch (error) {
    console.error('开始任务失败:', error);
    ElMessage.error('开始任务失败，请重试');
  }
};

// 重新尝试失败的任务
const retryTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      '重新尝试此任务将重置任务状态，是否继续？',
      '确认重新尝试',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 重置任务状态
    const taskIndex = myTasks.value.findIndex(t => t.id === task.id);
    if (taskIndex !== -1) {
      myTasks.value[taskIndex].status = 'pending';
      myTasks.value[taskIndex].startTime = null;
      myTasks.value[taskIndex].failedDate = null;
      myTasks.value[taskIndex].failureReason = null;
      myTasks.value[taskIndex].progress = 0;

      // 保存到本地存储（临时）
      localStorage.setItem('myTasks', JSON.stringify(myTasks.value));

      ElMessage.success('任务已重置，可以重新开始！');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新尝试任务失败:', error);
      ElMessage.error('重新尝试失败，请重试');
    }
  }
};

// 查看失败详情
const viewFailureDetails = (task) => {
  ElMessageBox.alert(
    `任务名称: ${task.title}\n失败时间: ${task.failedDate ? formatDate(task.failedDate) : '未知'}\n失败原因: ${task.failureReason || '超时未完成'}\n积分惩罚: -${Math.round(task.points * 0.5)}\n\n提示: 可以点击"重新尝试"来重置任务状态。`,
    '任务失败详情',
    {
      confirmButtonText: '知道了',
      type: 'error',
    }
  );
};

// 查看任务证据详情（与监督端统一）
const viewTaskEvidence = (task) => {
  // {{ AURA-X: Modify - 优化显示格式，提升可读性. Approval: 寸止(ID:1734684003). }}
  const timeSpent = task.timeSpent ? `${Math.round(task.timeSpent / 60)} 分钟` : '未记录';
  const ratingText = task.selfRating ? `${task.selfRating}/5 星` : '未评分';
  const submissionDate = task.completionDate || task.submittedDate;
  const formattedDate = submissionDate ? formatDate(submissionDate) : '未知';

  // 获取完成情况描述 - 优先使用evidence字段，其次是completionDescription
  const completionDescription = task.evidence || task.completionDescription || task.completionEvidence || '无';

  // 状态显示
  const statusText = task.status === 'completed' ? '✅ 已完成' : task.status === 'submitted' ? '⏳ 等待审核' : '🔄 进行中';

  // 星级评分显示
  const starDisplay = task.selfRating ? '★'.repeat(task.selfRating) + '☆'.repeat(5 - task.selfRating) : '未评分';

  // 使用HTML格式构建更好的显示效果
  const htmlContent = `
    <div style="text-align: left; line-height: 1.6; font-size: 14px;">
      <div style="border-bottom: 2px solid #e6f7ff; padding-bottom: 10px; margin-bottom: 15px;">
        <h4 style="margin: 0; color: #1890ff;">📝 ${task.title}</h4>
      </div>

      <div style="margin-bottom: 15px;">
        <p style="margin: 5px 0;"><strong>⏰ 提交时间：</strong>${formattedDate}</p>
        <p style="margin: 5px 0;"><strong>📊 任务状态：</strong>${statusText}</p>
        <p style="margin: 5px 0;"><strong>💎 获得积分：</strong>+${task.points}</p>
        <p style="margin: 5px 0;"><strong>⭐ 自我评分：</strong>${starDisplay} (${ratingText})</p>
        <p style="margin: 5px 0;"><strong>⏱️ 完成时长：</strong>${timeSpent}</p>
      </div>

      <div style="background: #f6ffed; padding: 10px; border-radius: 6px; margin-bottom: 15px;">
        <h5 style="margin: 0 0 8px 0; color: #52c41a;">📖 完成情况描述</h5>
        <p style="margin: 0; white-space: pre-wrap;">${completionDescription}</p>
      </div>

      <div style="background: #fff7e6; padding: 10px; border-radius: 6px;">
        <h5 style="margin: 0 0 8px 0; color: #fa8c16;">💭 反思总结</h5>
        <p style="margin: 0; white-space: pre-wrap;">${task.reflection || '暂无反思内容'}</p>
      </div>
    </div>
  `;

  ElMessageBox({
    title: '🎯 任务提交详情',
    dangerouslyUseHTMLString: true,
    message: htmlContent,
    confirmButtonText: '确定',
    type: 'info',
    customStyle: {
      width: '520px'
    }
  });
};

// 查看提交详情
const viewSubmissionDetails = (task) => {
  const timeSpent = task.timeSpent ? `${Math.round(task.timeSpent / 60)} 分钟` : '未记录';
  const ratingText = task.selfRating ? `${task.selfRating}/5 星` : '未评分';

  ElMessageBox.alert(
    `任务名称: ${task.title}\n提交时间: ${task.completionDate ? formatDate(task.completionDate) : '未知'}\n状态: 等待老师审核\n自我评分: ${ratingText}\n用时: ${timeSpent}\n\n完成证据:\n${task.evidence || task.completionEvidence || '无'}\n\n反思总结:\n${task.reflection || '无'}`,
    '任务提交详情',
    {
      confirmButtonText: '确定',
      type: 'info'
    }
  );
};

// {{ AURA-X: Add - 添加查看拒绝详情和重新提交功能. Approval: 寸止(ID:1734684007). }}
// 查看拒绝详情
const viewRejectionDetails = (task) => {
  const timeSpent = task.timeSpent ? `${Math.round(task.timeSpent / 60)} 分钟` : '未记录';
  const ratingText = task.selfRating ? `${task.selfRating}/5 星` : '未评分';
  const rejectionDate = task.rejectedDate || task.reviewedDate;
  const formattedDate = rejectionDate ? formatDate(rejectionDate) : '未知';
  const starDisplay = task.selfRating ? '★'.repeat(task.selfRating) + '☆'.repeat(5 - task.selfRating) : '未评分';

  const htmlContent = `
    <div style="text-align: left; line-height: 1.6; font-size: 14px;">
      <div style="border-bottom: 2px solid #fee2e2; padding-bottom: 10px; margin-bottom: 15px;">
        <h4 style="margin: 0; color: #dc2626;">❌ ${task.title}</h4>
      </div>

      <div style="margin-bottom: 15px;">
        <p style="margin: 5px 0;"><strong>📅 拒绝时间：</strong>${formattedDate}</p>
        <p style="margin: 5px 0;"><strong>⭐ 自我评分：</strong>${starDisplay} (${ratingText})</p>
        <p style="margin: 5px 0;"><strong>⏱️ 完成时长：</strong>${timeSpent}</p>
      </div>

      <div style="background: #fef2f2; padding: 10px; border-radius: 6px; margin-bottom: 15px; border-left: 4px solid #dc2626;">
        <h5 style="margin: 0 0 8px 0; color: #dc2626;">📝 拒绝原因</h5>
        <p style="margin: 0; white-space: pre-wrap;">${task.rejectionReason || task.feedback || '老师未提供具体原因'}</p>
      </div>

      <div style="background: #f6ffed; padding: 10px; border-radius: 6px; margin-bottom: 15px;">
        <h5 style="margin: 0 0 8px 0; color: #52c41a;">📖 原提交内容</h5>
        <p style="margin: 0; white-space: pre-wrap;">${task.evidence || task.completionDescription || task.completionEvidence || '无'}</p>
      </div>

      <div style="background: #fff7e6; padding: 10px; border-radius: 6px;">
        <h5 style="margin: 0 0 8px 0; color: #fa8c16;">💭 原反思总结</h5>
        <p style="margin: 0; white-space: pre-wrap;">${task.reflection || '无'}</p>
      </div>
    </div>
  `;

  ElMessageBox({
    title: '❌ 任务拒绝详情',
    dangerouslyUseHTMLString: true,
    message: htmlContent,
    confirmButtonText: '确定',
    type: 'error',
    customStyle: {
      width: '520px'
    }
  });
};

// 重新提交任务（被拒绝的任务）
const resubmitTask = (task) => {
  selectedTask.value = task;

  // 重置表单数据，但保留原有内容作为参考
  completionForm.value = {
    description: task.evidence || task.completionDescription || '',
    timeSpent: task.timeSpent || 30,
    selfRating: task.selfRating || 3,
    reflection: task.reflection || '',
    integrityPledge: false
  };

  // 显示完成对话框
  completeDialogVisible.value = true;
};

// {{ AURA-X: Add - 添加已提交任务的重新提交功能. Approval: 寸止(ID:1734684013). }}
// 重新提交已提交的任务
const resubmitSubmittedTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      '重新提交将覆盖当前的提交内容，是否继续？',
      '确认重新提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    selectedTask.value = task;

    // 重置表单数据，保留原有内容作为参考
    completionForm.value = {
      description: task.evidence || task.completionDescription || '',
      timeSpent: task.timeSpent || 30,
      selfRating: task.selfRating || 3,
      reflection: task.reflection || '',
      integrityPledge: false
    };

    // 显示完成对话框
    completeDialogVisible.value = true;
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新提交失败:', error);
      ElMessage.error('重新提交失败，请重试');
    }
  }
};



const completeTask = (task) => {
  selectedTask.value = task;

  // 根据任务目标时间设置默认时长
  let defaultTimeSpent = 30; // 默认30分钟
  if (task.target) {
    const target = task.target.toLowerCase();

    // 处理时间范围格式，如"45分钟-1.5小时"
    if (target.includes('-')) {
      // 提取范围的最小值
      const rangeParts = target.split('-');
      const minTimeStr = rangeParts[0].trim();

      if (minTimeStr.includes('小时')) {
        const hours = parseFloat(minTimeStr.match(/(\d+(?:\.\d+)?)/)?.[1] || 1);
        defaultTimeSpent = Math.round(hours * 60);
      } else if (minTimeStr.includes('分钟')) {
        const minutes = parseInt(minTimeStr.match(/(\d+)/)?.[1] || 30);
        defaultTimeSpent = minutes;
      }
    } else {
      // 处理单一时间值
      if (target.includes('小时')) {
        const hours = parseFloat(target.match(/(\d+(?:\.\d+)?)/)?.[1] || 1);
        defaultTimeSpent = Math.round(hours * 60);
      } else if (target.includes('分钟')) {
        const minutes = parseInt(target.match(/(\d+)/)?.[1] || 30);
        defaultTimeSpent = minutes;
      }
    }
  }

  completionForm.value = {
    description: '',
    timeSpent: defaultTimeSpent,
    selfRating: 3,
    reflection: '',
    integrityPledge: false
  };
  completeDialogVisible.value = true;
};

const updateProgress = (task) => {
  selectedTask.value = task;
  progressForm.value = {
    progress: task.progress || 0,
    note: '',
    timeSpent: 0
  };
  progressDialogVisible.value = true;
};

const submitProgressUpdate = () => {
  if (!progressForm.value.note) {
    ElMessage.warning('请填写进度说明');
    return;
  }
  
  try {
    const taskIndex = myTasks.value.findIndex(t => t.id === selectedTask.value.id);
    if (taskIndex !== -1) {
      // 更新进度
      myTasks.value[taskIndex].progress = progressForm.value.progress;
      
      // 添加进度记录
      if (!myTasks.value[taskIndex].progressLogs) {
        myTasks.value[taskIndex].progressLogs = [];
      }
      
      myTasks.value[taskIndex].progressLogs.push({
        date: new Date().toISOString(),
        progress: progressForm.value.progress,
        note: progressForm.value.note,
        timeSpent: progressForm.value.timeSpent
      });
      
      // 更新总学习时间
      myTasks.value[taskIndex].totalTimeSpent = (myTasks.value[taskIndex].totalTimeSpent || 0) + progressForm.value.timeSpent;
      
      // 更新最后活跃时间
      myTasks.value[taskIndex].lastActiveTime = new Date().toISOString();
      
      saveTasksToStorage();
      ElMessage.success('进度更新成功！');
      
      // 如果进度达到100%，提示完成任务
      if (progressForm.value.progress === 100) {
        ElMessageBox.confirm(
          '您的进度已达到100%，是否要标记任务为已完成？',
          '提示',
          {
            confirmButtonText: '完成任务',
            cancelButtonText: '稍后完成',
            type: 'info'
          }
        ).then(() => {
          completeTask(myTasks.value[taskIndex]);
        }).catch(() => {});
      }
    }
    
    progressDialogVisible.value = false;
  } catch (error) {
    console.error('更新进度失败:', error);
    ElMessage.error('更新失败，请重试');
  }
};

const submitTaskCompletion = async () => {
  if (!completionForm.value.description) {
    ElMessage.warning('请填写完成情况描述');
    return;
  }

  if (!completionForm.value.integrityPledge) {
    ElMessage.warning('请确认诚信承诺');
    return;
  }

  try {
    console.log('=== MyTasks提交调试 ===');
    console.log('选中的任务:', selectedTask.value);
    console.log('完成表单:', completionForm.value);

    const taskIndex = myTasks.value.findIndex(t => t.id === selectedTask.value.id);
    console.log('任务索引:', taskIndex);

    if (taskIndex !== -1) {
      const task = myTasks.value[taskIndex];
      console.log('找到的任务:', task);

      // {{ AURA-X: Modify - 使用统一的学生端数据源提交任务. Approval: 寸止(ID:1734684000). }}
      // 使用统一的学生端数据源提交任务完成
      const success = await studentStore.submitTaskCompletion(task.id, {
        evidence: completionForm.value.description,
        timeSpent: completionForm.value.timeSpent,
        selfRating: completionForm.value.selfRating,
        reflection: completionForm.value.reflection
      });

      if (success) {
        // 任务提交成功，数据已在store中更新（状态为 submitted，待审核）
        // 修复石奖励按“完成”口径在刷新或审核通过后统一计算
        completeDialogVisible.value = false;
        return;
      }

      // 更新本地任务状态为已提交待审核
      myTasks.value[taskIndex].status = 'submitted';
      myTasks.value[taskIndex].completionDate = new Date().toISOString();
      myTasks.value[taskIndex].completionDescription = completionForm.value.description;
      myTasks.value[taskIndex].selfRating = completionForm.value.selfRating;
      myTasks.value[taskIndex].reflection = completionForm.value.reflection;
      myTasks.value[taskIndex].endTime = new Date().toISOString();
      myTasks.value[taskIndex].progress = 100;
      
      // 更新总学习时间
      myTasks.value[taskIndex].totalTimeSpent = (myTasks.value[taskIndex].totalTimeSpent || 0) + completionForm.value.timeSpent;
      
      // 添加完成记录
      if (!myTasks.value[taskIndex].completionLogs) {
        myTasks.value[taskIndex].completionLogs = [];
      }
      
      myTasks.value[taskIndex].completionLogs.push({
        date: new Date().toISOString(),
        description: completionForm.value.description,
        timeSpent: completionForm.value.timeSpent,
        selfRating: completionForm.value.selfRating,
        reflection: completionForm.value.reflection
      });
      
      // 更新积分
      userPoints.value += myTasks.value[taskIndex].points;
      localStorage.setItem(STORAGE_KEY_POINTS, userPoints.value);
      
      // 更新连续天数
      updateConsecutiveDays();
      
      saveTasksToStorage();
      ElMessage.success('恭喜！任务完成成功！');
      
      // 修复石奖励：改为“每完成12个任务奖励1颗”，不在本地提交时发放
    }
    
    completeDialogVisible.value = false;
  } catch (error) {
    console.error('提交任务完成失败:', error);
    ElMessage.error('提交失败，请重试');
  }
};

const abandonTask = (task) => {
  ElMessageBox.confirm(
    '确定要放弃此任务吗？这将导致积分惩罚，并在日历上留下裂痕。',
    '警告',
    {
      confirmButtonText: '确认放弃',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    selectedTask.value = task;
    failTask(task);
  }).catch(() => {});
};

const failTask = (task) => {
  try {
    const taskIndex = myTasks.value.findIndex(t => t.id === task.id);
    if (taskIndex !== -1) {
      // 更新任务状态
      myTasks.value[taskIndex].status = 'failed';
      myTasks.value[taskIndex].failedDate = new Date().toISOString();
      myTasks.value[taskIndex].endTime = new Date().toISOString();
      
      // 扣除积分
      const penaltyPoints = Math.round(task.points * 0.5);
      userPoints.value = Math.max(0, userPoints.value - penaltyPoints);
      localStorage.setItem(STORAGE_KEY_POINTS, userPoints.value);
      
      // 重置连续天数
      consecutiveDays.value = 0;
      localStorage.setItem(STORAGE_KEY_STREAK, 0);
      
      saveTasksToStorage();
      
      // 显示惩罚对话框
      failureDialogVisible.value = true;
    }
  } catch (error) {
    console.error('任务失败处理错误:', error);
    ElMessage.error('操作失败，请重试');
  }
};

// 取消放弃任务
const cancelAbandon = () => {
  failureDialogVisible.value = false;
  selectedTask.value = null;
  ElMessage.info('已取消放弃任务');
};

// 确认失败
const acknowledgeFailure = () => {
  failureDialogVisible.value = false;
  selectedTask.value = null;
  ElMessage.warning('任务已放弃');
};

const repairCrack = () => {
  if (repairStones.value <= 0) {
    ElMessage.warning('修复石不足，无法修复裂痕');
    // 引导用户去积分商城购买修复石
    ElMessageBox.confirm(
      '您的修复石不足，是否前往积分商城购买？',
      '提示',
      {
        confirmButtonText: '去购买',
        cancelButtonText: '取消',
        type: 'info'
      }
    ).then(() => {
      router.push('/student/points-shop');
    }).catch(() => {});
    return;
  }
  
  ElMessageBox.confirm(
    '确定要使用1颗修复石修复此日期的裂痕吗？',
    '确认修复',
    {
      confirmButtonText: '确认修复',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    try {
      // 找到该日期的所有失败任务
      const tasksToRepair = tasksForSelectedDay.value.filter(task => task.status === 'failed' && !task.repaired);
      
      if (tasksToRepair.length > 0) {
        // 标记所有任务为已修复
        tasksToRepair.forEach(task => {
          const taskIndex = myTasks.value.findIndex(t => t.id === task.id);
          if (taskIndex !== -1) {
            myTasks.value[taskIndex].repaired = true;
          }
        });
        
        // 消耗修复石
        repairStones.value -= 1;
        localStorage.setItem(STORAGE_KEY_STONES, repairStones.value);
        
        saveTasksToStorage();
        
        // 显示修复成功的动画效果
        ElMessage({
          message: '裂痕修复成功！',
          type: 'success',
          duration: 2000,
          showClose: false,
          customClass: 'repair-success-message'
        });
        
        // 添加修复记录
        if (!localStorage.getItem('repairHistory')) {
          localStorage.setItem('repairHistory', JSON.stringify([]));
        }
        
        const repairHistory = JSON.parse(localStorage.getItem('repairHistory'));
        repairHistory.push({
          date: new Date().toISOString(),
          repairedDate: selectedDate.value.toISOString(),
          tasksCount: tasksToRepair.length
        });
        
        localStorage.setItem('repairHistory', JSON.stringify(repairHistory));
        
        // 更新连续天数
        updateConsecutiveDaysAfterRepair();
      } else {
        ElMessage.warning('该日期没有需要修复的裂痕');
      }
    } catch (error) {
      console.error('修复裂痕失败:', error);
      ElMessage.error('修复失败，请重试');
    }
  }).catch(() => {});
};

// 修复裂痕后更新连续天数
const updateConsecutiveDaysAfterRepair = () => {
  // 检查是否需要恢复连续天数
  const today = new Date();
  const selectedDateStr = selectedDate.value.toDateString();
  const todayStr = today.toDateString();
  
  // 如果修复的是今天或昨天的裂痕，可能需要恢复连续天数
  if (selectedDateStr === todayStr || isYesterday(selectedDate.value)) {
    // 检查今天是否有完成的任务
    const hasCompletedToday = myTasks.value.some(task => {
      if (task.status !== 'completed') return false;
      const completionDate = new Date(task.completionDate);
      return completionDate.toDateString() === todayStr;
    });
    
    // 如果今天有完成的任务，恢复连续天数
    if (hasCompletedToday) {
      consecutiveDays.value = Math.max(1, consecutiveDays.value + 1);
      localStorage.setItem(STORAGE_KEY_STREAK, consecutiveDays.value);
      ElMessage.success(`连续完成天数已恢复为 ${consecutiveDays.value} 天！`);
    }
  }
};

// 判断日期是否是昨天
const isYesterday = (date) => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.toDateString() === yesterday.toDateString();
};

const goToTaskSquare = () => {
  router.push('/student/tasks');
};

// 手动刷新任务数据
const refreshTaskData = async () => {
  try {
    refreshLoading.value = true;
    console.log('=== 手动刷新任务数据 ===');

    // 重新加载学生数据，这会触发任务数据的更新
    await studentStore.loadMyTasks();

    // {{ AURA-X: Add - 刷新时同时更新今日学习时长. Approval: 寸止(ID:1734684024). }}
    // 刷新今日学习时长统计
    await loadTodayStudyTime();

    ElMessage.success('任务状态已刷新');
    console.log('任务数据刷新完成');

    // {{ AURA-X: Add - 刷新后检查任务状态变化. Approval: 寸止(ID:1734684019). }}
    // 检查是否有任务状态从submitted变为rejected
    const rejectedTasks = studentStore.myTasks.filter(task => task.status === 'rejected');
    if (rejectedTasks.length > 0) {
      ElMessage.warning(`有 ${rejectedTasks.length} 个任务被拒绝，请查看详情并重新提交`);
    }
  } catch (error) {
    console.error('刷新任务数据失败:', error);
    ElMessage.error('刷新失败，请重试');
  } finally {
    refreshLoading.value = false;
  }
};

const updateConsecutiveDays = () => {
  // 检查今天是否已经有完成的任务
  const today = new Date();
  const todayString = today.toDateString();
  
  // 检查昨天是否有完成的任务
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayString = yesterday.toDateString();
  
  const hasCompletedToday = myTasks.value.some(task => {
    if (task.status !== 'completed') return false;
    const completionDate = new Date(task.completionDate);
    return completionDate.toDateString() === todayString;
  });
  
  const hasCompletedYesterday = myTasks.value.some(task => {
    if (task.status !== 'completed') return false;
    const completionDate = new Date(task.completionDate);
    return completionDate.toDateString() === yesterdayString;
  });
  
  if (!hasCompletedToday) return;

  // 今日有完成：若昨有则+1，否则置为1
  if (hasCompletedYesterday) {
    consecutiveDays.value = Math.max(1, (consecutiveDays.value || 0) + 1);
  } else {
    consecutiveDays.value = 1;
  }
  localStorage.setItem(STORAGE_KEY_STREAK, consecutiveDays.value);
};

// 存储和加载数据
const saveTasksToStorage = () => {
  localStorage.setItem(STORAGE_KEY_TASKS, JSON.stringify(myTasks.value));
};

// 移除loadTasksFromStorage函数，直接使用computed响应式数据
// const loadTasksFromStorage = async () => {
//   // 已改为使用computed，不需要手动加载
// };

// 加载其他数据（修复石、积分等）
const loadOtherData = () => {
  // 加载修复石数量，同时检查积分商城中的购买记录
  const storedStones = localStorage.getItem(STORAGE_KEY_STONES);
  if (storedStones) {
    repairStones.value = parseInt(storedStones);
  } else {
    // 初始赠送1颗修复石
    repairStones.value = 1;
    localStorage.setItem(STORAGE_KEY_STONES, repairStones.value);
  }

  // 检查积分商城中是否购买了修复石
  checkRepairStonePurchases();

  // 依据“已完成任务总数”计算应得修复石：每12个奖励1颗
  try {
    const completedCount = myTasks.value.filter(t => t.status === 'completed').length;
    const earnedStones = Math.floor(completedCount / 12);
    if (earnedStones > repairStones.value) {
      repairStones.value = earnedStones;
      localStorage.setItem(STORAGE_KEY_STONES, repairStones.value);
    }
  } catch {}

  const storedStreak = localStorage.getItem(STORAGE_KEY_STREAK);
  if (storedStreak) {
    consecutiveDays.value = parseInt(storedStreak);
  }

  const storedPoints = localStorage.getItem(STORAGE_KEY_POINTS);
  if (storedPoints) {
    userPoints.value = parseInt(storedPoints);
  }
};

// 检查积分商城中的修复石购买记录
const checkRepairStonePurchases = () => {
  try {
    // 获取购买历史
    const purchaseHistory = localStorage.getItem(STORAGE_KEY_PURCHASE_HISTORY);
    if (!purchaseHistory) return;
    
    const purchases = JSON.parse(purchaseHistory);
    
    // 获取商品列表，找到修复石商品的ID
    const products = localStorage.getItem(STORAGE_KEY_PRODUCTS);
    if (!products) return;
    
    const productList = JSON.parse(products);
    const repairStoneProduct = productList.find(p => p.name === '修复石');
    
    if (!repairStoneProduct) return;
    
    // 获取已处理的购买记录
    const processedPurchases = localStorage.getItem('processedRepairStonePurchases') || '[]';
    const processedIds = JSON.parse(processedPurchases);
    
    // 查找未处理的修复石购买记录
    const newRepairStonePurchases = purchases.filter(
      p => p.productId === repairStoneProduct.id && !processedIds.includes(p.id)
    );
    
    if (newRepairStonePurchases.length > 0) {
      // 更新修复石数量
      newRepairStonePurchases.forEach(() => {
        repairStones.value += 1;
      });
      
      // 保存修复石数量
      localStorage.setItem(STORAGE_KEY_STONES, repairStones.value);
      
      // 更新已处理的购买记录
      const updatedProcessedIds = [
        ...processedIds,
        ...newRepairStonePurchases.map(p => p.id)
      ];
      localStorage.setItem('processedRepairStonePurchases', JSON.stringify(updatedProcessedIds));
      
      // 显示提示
      if (newRepairStonePurchases.length > 0) {
        ElMessage.success(`已添加 ${newRepairStonePurchases.length} 颗从商城购买的修复石`);
      }
    }
  } catch (error) {
    console.error('检查修复石购买记录失败:', error);
  }
};

// {{ AURA-X: Add - 添加加载今日学习时长的方法. Approval: 寸止(ID:1734684025). }}
// 加载今日学习时长统计
const loadTodayStudyTime = async () => {
  try {
    const response = await taskAPI.getTodayStudyTime();
    if (response && response.data) {
      // 将分钟转换为小时，保留一位小数
      const hours = (response.data.totalMinutes || 0) / 60;
      todayStudyHours.value = hours.toFixed(1);
    }
  } catch (error) {
    console.error('获取今日学习时长失败:', error);
    // 失败时显示默认值
    todayStudyHours.value = '0.0';
  }
};

// 生命周期钩子
onMounted(async () => {
  // 初始化学生数据
  await studentStore.initStudentData();

  // 加载其他数据
  loadOtherData();

  // {{ AURA-X: Add - 初始化时加载今日学习时长. Approval: 寸止(ID:1734684026). }}
  // 加载今日学习时长统计
  await loadTodayStudyTime();

  selectedDate.value = new Date();

  // 检查是否有过期任务
  checkExpiredTasks();

  // 初始化一次修复石（按完成任务数）
  try {
    const completedCount = myTasks.value.filter(t => t.status === 'completed').length;
    const earnedStones = Math.floor(completedCount / 12);
    if (earnedStones > (parseInt(localStorage.getItem(STORAGE_KEY_STONES) || '0'))) {
      repairStones.value = earnedStones;
      localStorage.setItem(STORAGE_KEY_STONES, earnedStones);
    }
  } catch {}
});




// 检查过期任务
const checkExpiredTasks = () => {
  const now = new Date();
  
  myTasks.value.forEach(task => {
    if (task.status === 'in_progress' && task.deadline) {
      const deadline = new Date(task.deadline);
      if (deadline < now) {
        // 任务已过期，标记为失败
        if (!task.status === 'failed') {
          failTask(task);
        }
      }
    }
  });
};

// 监听器
watch(selectedDate, () => {
  // 当选择的日期变化时，可以执行一些操作
});
</script>

<style scoped>
.my-tasks-container {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
  position: relative;
}

.my-tasks-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(161, 196, 253, 0.03)' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  pointer-events: none;
  z-index: 0;
}

/* 顶部卡片 */
.header-card {
  background: linear-gradient(120deg, #a6c0fe, #f68084);
  border-radius: 24px;
  padding: 32px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(166, 192, 254, 0.3);
  z-index: 1;
}

.header-card::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
  animation: rotate 30s linear infinite;
}

.header-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header-content {
  position: relative;
  z-index: 2;
}

.header-title {
  text-align: center;
  margin-bottom: 25px;
}

.header-title h1 {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: 1px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background: linear-gradient(to right, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.header-title p {
  font-size: 1rem;
  margin-top: 8px;
  opacity: 0.9;
  font-weight: 300;
  letter-spacing: 0.5px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 10px;
  margin-top: 16px;
}

.stat-box {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 12px 14px;
  min-width: 100px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.stat-icon {
  width: 32px;
  height: 32px;
  margin-right: 15px;
  position: relative;
}

.repair-stone {
  background: linear-gradient(135deg, #c2e9fb, #a1c4fd);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  animation: pulse 3s infinite;
  position: relative;
}

.repair-stone::after {
  content: '';
  position: absolute;
  top: 25%;
  left: 25%;
  width: 50%;
  height: 50%;
  background: rgba(255, 255, 255, 0.7);
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  animation: sparkle 5s infinite;
}

.completion-rate {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: conic-gradient(#4263eb var(--rate), #e9ecef var(--rate));
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2) inset;
  position: relative;
}

.completion-rate::after {
  content: '';
  position: absolute;
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.streak-days {
  background: linear-gradient(to top, #ff9a9e, #fad0c4);
  clip-path: polygon(50% 0%, 80% 30%, 90% 60%, 50% 100%, 10% 60%, 20% 30%);
  animation: flicker 2s infinite alternate;
  position: relative;
}

.streak-days::after {
  content: '';
  position: absolute;
  top: 30%;
  left: 30%;
  width: 40%;
  height: 40%;
  background: rgba(255, 255, 255, 0.8);
  clip-path: polygon(50% 0%, 70% 50%, 50% 100%, 30% 50%);
  animation: innerFlame 1s infinite alternate;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 5px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 0.85rem;
  opacity: 0.9;
  font-weight: 300;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

@keyframes sparkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes flicker {
  0%, 100% { transform: scale(1); filter: brightness(1); }
  50% { transform: scale(0.95); filter: brightness(0.9); }
}

@keyframes innerFlame {
  0% { transform: scale(0.8); opacity: 0.5; }
  100% { transform: scale(1.2); opacity: 0.8; }
}

/* 日历部分 */
.content-card {
  background-color: white;
  border-radius: 24px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.content-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05);
}

.content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #a6c0fe, #f68084, #a6c0fe);
  background-size: 200% 100%;
  animation: gradientMove 5s linear infinite;
}

@keyframes gradientMove {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}

.calendar-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(166, 192, 254, 0.05) 0%, rgba(166, 192, 254, 0) 70%);
  border-radius: 50%;
  pointer-events: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  position: relative;
}

/* {{ AURA-X: Add - 添加今日学习时长显示样式. Approval: 寸止(ID:1734684027). }} */
.study-time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.study-time-label {
  font-size: 14px;
  opacity: 0.9;
}

.study-time-value {
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.refresh-button {
  font-size: 14px;
}

.card-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  position: relative;
  padding-left: 15px;
}

.card-header h2::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(to bottom, #a6c0fe, #f68084);
  border-radius: 2px;
}

.month-nav {
  display: flex;
  align-items: center;
  gap: 15px;
}

.month-year {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
  min-width: 120px;
  text-align: center;
}

.nav-button {
  color: #4263eb;
  border-color: #e6eeff;
  transition: all 0.3s ease;
}

.nav-button:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 10px rgba(66, 99, 235, 0.2);
}

.calendar {
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #eaeaea;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: linear-gradient(to right, #f8f9fa, #f0f7ff);
  border-bottom: 1px solid #eaeaea;
}

.weekday {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: #4263eb;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: #ffffff;
}

.day {
  border-right: 1px solid #eaeaea;
  border-bottom: 1px solid #eaeaea;
  padding: 10px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 70px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.day:nth-child(7n) {
  border-right: none;
}

.day:nth-last-child(-n+7) {
  border-bottom: none;
}

.day:hover {
  background-color: #f0f7ff;
  transform: scale(1.05);
  z-index: 2;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.day.selected {
  background-color: #e8f0fe;
  box-shadow: 0 0 0 2px #4263eb inset;
}

.day.today {
  background-color: #e8f0fe;
}

.day.other-month {
  color: #ccc;
  background-color: #fafafa;
}

.day-number {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 6px;
  display: block;
  height: 24px;
  width: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.day.today .day-number {
  background-color: #4263eb;
  color: white;
  box-shadow: 0 3px 8px rgba(66, 99, 235, 0.3);
}

.day.has-tasks .day-number {
  color: #4263eb;
}

.day.has-completed .day-number {
  color: #10b981;
}

.day.has-failed .day-number {
  color: #ef4444;
}

.task-dots {
  display: flex;
  flex-direction: column;
  gap: 3px;
  justify-content: center;
  margin-top: 5px;
}

.dots-row {
  display: flex;
  gap: 3px;
  justify-content: center;
}

.task-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #4263eb;
  transition: transform 0.3s ease;
}

.task-dot.completed {
  background-color: #10b981;
  box-shadow: 0 0 2px rgba(16, 185, 129, 0.5);
}

.task-dot.failed {
  background-color: #ef4444;
  box-shadow: 0 0 2px rgba(239, 68, 68, 0.5);
}

.task-dot.in-progress {
  background-color: #f59e0b;
  box-shadow: 0 0 2px rgba(245, 158, 11, 0.5);
}

.day:hover .task-dot {
  transform: scale(1.3);
}

.day.selected .task-dot {
  transform: scale(1.4);
  animation: dotPulse 1.5s infinite alternate;
}

@keyframes dotPulse {
  0% { transform: scale(1.2); }
  100% { transform: scale(1.5); }
}

.more-tasks {
  font-size: 0.7rem;
  color: #666;
  margin-top: 2px;
}

.day.has-crack {
  position: relative;
}

.crack-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path d="M50,0 L45,30 L60,40 L40,60 L55,70 L50,100" stroke="rgba(239, 68, 68, 0.5)" stroke-width="2" fill="none"/><path d="M50,0 L55,25 L40,45 L60,65 L45,75 L50,100" stroke="rgba(239, 68, 68, 0.3)" stroke-width="1" fill="none"/></svg>');
  background-size: cover;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: crackPulse 2s infinite alternate;
  z-index: 10; /* 确保裂痕在最上层 */
}

.day.has-crack .crack-overlay {
  opacity: 1;
}

.day:hover .crack-overlay {
  filter: drop-shadow(0 0 3px rgba(239, 68, 68, 0.5));
}

.day.has-crack.selected .crack-overlay {
  filter: drop-shadow(0 0 5px rgba(239, 68, 68, 0.7));
  animation: crackPulseSelected 1.5s infinite alternate;
}

@keyframes crackPulseSelected {
  0% { opacity: 0.6; filter: drop-shadow(0 0 3px rgba(239, 68, 68, 0.5)); }
  100% { opacity: 1; filter: drop-shadow(0 0 8px rgba(239, 68, 68, 0.8)); }
}

.day.crack-level-1 .crack-overlay {
  border: 2px solid rgba(239, 68, 68, 0.3);
  border-radius: 0;
  background: linear-gradient(135deg, transparent 45%, rgba(239, 68, 68, 0.5) 50%, transparent 55%);
  opacity: 1;
  box-shadow: inset 0 0 10px rgba(239, 68, 68, 0.2);
}

.day.crack-level-2 .crack-overlay {
  border: 2px solid rgba(239, 68, 68, 0.4);
  border-radius: 0;
  background: 
    linear-gradient(135deg, transparent 45%, rgba(239, 68, 68, 0.6) 50%, transparent 55%),
    linear-gradient(45deg, transparent 45%, rgba(239, 68, 68, 0.6) 50%, transparent 55%);
  opacity: 1;
  box-shadow: inset 0 0 15px rgba(239, 68, 68, 0.3);
}

.day.crack-level-3 .crack-overlay {
  border: 3px solid rgba(239, 68, 68, 0.5);
  border-radius: 0;
  background: 
    linear-gradient(135deg, transparent 45%, rgba(239, 68, 68, 0.7) 50%, transparent 55%),
    linear-gradient(45deg, transparent 45%, rgba(239, 68, 68, 0.7) 50%, transparent 55%),
    linear-gradient(90deg, transparent 45%, rgba(239, 68, 68, 0.7) 50%, transparent 55%);
  opacity: 1;
  box-shadow: inset 0 0 20px rgba(239, 68, 68, 0.4);
}

/* 添加一个明显的红色边框标记，确保能看到效果 */
.day.has-crack {
  position: relative;
  overflow: visible;
}

.day.has-crack::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #ef4444;
  z-index: 5;
  pointer-events: none;
}

@keyframes crackPulse {
  0% { opacity: 0.5; }
  100% { opacity: 0.8; }
}

/* 任务详情区域 */
.tasks-section {
  background-color: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.tasks-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, #a1c4fd, #c2e9fb);
}

.repair-button {
  margin-left: auto;
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.repair-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0) 100%);
  transform: rotate(45deg);
  animation: shimmerButton 3s infinite;
  z-index: 1;
}

.repair-button:active {
  transform: scale(0.95);
  box-shadow: 0 0 15px rgba(255, 154, 158, 0.7);
}

.repair-button .cost {
  font-size: 0.8rem;
  opacity: 0.9;
  position: relative;
  z-index: 2;
}

.repair-success-message {
  background: linear-gradient(135deg, #c2e9fb, #a1c4fd) !important;
  border: none !important;
  color: #333 !important;
  font-weight: 500 !important;
  box-shadow: 0 5px 15px rgba(161, 196, 253, 0.4) !important;
}

@keyframes shimmerButton {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 25px;
}

.task-card {
  display: flex;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #f0f7ff;
}

.task-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.task-card {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.task-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.task-status-indicator {
  width: 10px;
  background: linear-gradient(to bottom, #a1c4fd, #c2e9fb);
  position: relative;
  overflow: hidden;
}

.task-status-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: rgba(255, 255, 255, 0.3);
  animation: shimmerIndicator 2s infinite;
}

.task-card.completed .task-status-indicator {
  background: linear-gradient(to bottom, #84fab0, #8fd3f4);
}

.task-card.failed .task-status-indicator {
  background: linear-gradient(to bottom, #ff9a9e, #fad0c4);
}

.task-card.in-progress .task-status-indicator {
  background: linear-gradient(to bottom, #ffecd2, #fcb69f);
}

@keyframes shimmerIndicator {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(300%); }
}

.task-content {
  flex: 1;
  padding: 16px;
  background: linear-gradient(to bottom right, #ffffff, #fafcff);
  position: relative;
}

.task-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="100%" height="100%" fill="none"/><path d="M40,40 L160,40 L160,160 L40,160 Z" stroke="rgba(161, 196, 253, 0.1)" stroke-width="1" fill="none" stroke-dasharray="5,5"/></svg>');
  background-size: 200px 200px;
  opacity: 0.5;
  pointer-events: none;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(161, 196, 253, 0.2);
}

.task-meta {
  display: flex;
  gap: 12px;
}

.task-type, .task-subject {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.task-type:hover, .task-subject:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.task-type {
  background: linear-gradient(135deg, #e8f0fe, #d4e4fa);
  color: #4263eb;
}

.task-subject {
  background: linear-gradient(135deg, #f0f9ff, #d6eeff);
  color: #0284c7;
}

.subject-math {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  color: #10b981;
  border-left: 3px solid #10b981;
}

.subject-english {
  background: linear-gradient(135deg, #e0f2fe, #bae6fd);
  color: #0284c7;
  border-left: 3px solid #0284c7;
}

.subject-politics {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #e11d48;
  border-left: 3px solid #e11d48;
}

.subject-major {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #d97706;
  border-left: 3px solid #d97706;
}

.task-status {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.task-status::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.3), rgba(255,255,255,0));
  animation: shimmerStatus 2s infinite;
}

@keyframes shimmerStatus {
  0% { transform: translateX(0); }
  100% { transform: translateX(200%); }
}

.status-completed {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  color: #10b981;
}

.status-failed {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #ef4444;
}

.status-in_progress {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #f59e0b;
}

.task-title {
  font-size: 1.1rem;
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
  letter-spacing: 0.5px;
  position: relative;
  padding-bottom: 6px;
  display: inline-block;
}

.task-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, #a1c4fd, #c2e9fb);
  border-radius: 1px;
}

.task-description {
  color: #555;
  margin: 0 0 20px 0;
  line-height: 1.7;
  font-size: 1.05rem;
  position: relative;
  padding-left: 15px;
  border-left: 3px solid rgba(161, 196, 253, 0.3);
}

.task-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
  padding: 10px;
  background: rgba(249, 250, 251, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(161, 196, 253, 0.2);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 0.95rem;
  padding: 5px 10px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.detail-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
}

.detail-item.warning {
  color: #ef4444;
  background: rgba(254, 226, 226, 0.5);
  border-left: 3px solid #ef4444;
}

.task-progress {
  margin-bottom: 12px;
  padding: 10px;
  background: rgba(249, 250, 251, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(161, 196, 253, 0.2);
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.95rem;
  color: #555;
  font-weight: 500;
}

.task-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  border: none;
}

.action-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
  transform: rotate(45deg);
  animation: shimmerAction 3s infinite;
  z-index: 1;
  opacity: 0;
}

.action-button:hover::before {
  opacity: 1;
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.action-button .el-icon {
  z-index: 2;
}

@keyframes shimmerAction {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

.el-button[type="primary"] {
  background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
  border: none;
}

.el-button[type="danger"] {
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
  border: none;
}

.el-button:not([type="primary"]):not([type="danger"]) {
  background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
  color: #555;
  border: none;
}

.completion-info, .failure-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 15px;
  background: rgba(249, 250, 251, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(161, 196, 253, 0.2);
}

.completion-time, .failure-time, .self-rating, .failure-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 0.95rem;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.completion-time, .failure-time {
  border-left: 3px solid #a1c4fd;
}

.self-rating {
  border-left: 3px solid #84fab0;
}

.failure-message {
  color: #ef4444;
  border-left: 3px solid #ef4444;
  background: rgba(254, 226, 226, 0.5);
  animation: pulseWarning 2s infinite alternate;
}

@keyframes pulseWarning {
  0% { border-left-color: #ef4444; }
  100% { border-left-color: #fca5a5; }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 70px 0;
  text-align: center;
  background: linear-gradient(135deg, #f5f7fa, #f0f7ff);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.empty-state::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="5" fill="rgba(161, 196, 253, 0.1)"/><circle cx="80" cy="80" r="5" fill="rgba(161, 196, 253, 0.1)"/><circle cx="20" cy="80" r="5" fill="rgba(161, 196, 253, 0.1)"/><circle cx="80" cy="20" r="5" fill="rgba(161, 196, 253, 0.1)"/><circle cx="50" cy="50" r="10" fill="rgba(161, 196, 253, 0.1)"/></svg>');
  background-size: 100px 100px;
  opacity: 0.5;
}

.empty-illustration {
  width: 150px;
  height: 150px;
  position: relative;
  margin-bottom: 30px;
  animation: float 6s infinite ease-in-out;
}

.empty-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px dashed rgba(161, 196, 253, 0.5);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: rotate 20s infinite linear;
}

.empty-line {
  height: 4px;
  background: linear-gradient(to right, rgba(161, 196, 253, 0.3), rgba(194, 233, 251, 0.3));
  position: absolute;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.empty-line.line-1 {
  width: 70px;
  top: 30px;
  left: 30px;
  transform: rotate(-5deg);
  animation: pulse 3s infinite alternate;
}

.empty-line.line-2 {
  width: 90px;
  top: 70px;
  left: 20px;
  transform: rotate(5deg);
  animation: pulse 3s infinite alternate 1s;
}

.empty-line.line-3 {
  width: 50px;
  top: 110px;
  left: 40px;
  transform: rotate(-8deg);
  animation: pulse 3s infinite alternate 2s;
}

.empty-text {
  color: #555;
  margin-bottom: 25px;
  font-size: 1.2rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.empty-button {
  background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
  border: none;
  border-radius: 25px;
  padding: 12px 25px;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 5px 15px rgba(161, 196, 253, 0.4);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.empty-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
  transform: rotate(45deg);
  animation: shimmerEmpty 3s infinite;
  z-index: -1;
}

.empty-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 25px rgba(161, 196, 253, 0.5);
}

@keyframes shimmerEmpty {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

@keyframes rotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes pulse {
  0% { opacity: 0.5; transform: scaleX(0.95) rotate(-5deg); }
  100% { opacity: 0.8; transform: scaleX(1.05) rotate(5deg); }
}

/* 对话框样式 */
.completion-dialog, .progress-dialog, .failure-dialog {
  border-radius: 20px;
  overflow: hidden;
}

.el-dialog__header {
  background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
  padding: 20px;
  position: relative;
}

.el-dialog__title {
  color: white;
  font-weight: 600;
  letter-spacing: 1px;
  font-size: 1.3rem;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.el-dialog__body {
  background: linear-gradient(to bottom right, #ffffff, #fafcff);
  padding: 30px;
}

.el-dialog__footer {
  background: #f8fafc;
  border-top: 1px solid rgba(161, 196, 253, 0.2);
  padding: 15px 20px;
}

.complete-form, .progress-form {
  padding: 10px;
}

.task-preview {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(249, 250, 251, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(161, 196, 253, 0.2);
  position: relative;
}

.task-preview h3 {
  font-size: 1.3rem;
  margin: 0 0 15px 0;
  color: #333;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.task-tags {
  display: flex;
  gap: 12px;
  margin-top: 15px;
}

.task-type-tag, .task-subject-tag {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.task-type-tag {
  background: linear-gradient(135deg, #e8f0fe, #d4e4fa);
  color: #4263eb;
}

.completion-warning {
  margin-bottom: 25px;
}

.upload-tip {
  font-size: 0.95rem;
  color: #555;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(249, 250, 251, 0.7);
  border-radius: 8px;
  border-left: 3px solid #a1c4fd;
}

.rating-container {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  background: rgba(249, 250, 251, 0.5);
  border-radius: 8px;
}

.rating-text {
  color: #555;
  font-weight: 500;
}

.integrity-pledge {
  margin-top: 25px;
  padding: 15px;
  background: rgba(249, 250, 251, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(161, 196, 253, 0.2);
}

.el-form-item__label {
  font-weight: 500;
  color: #333;
  font-size: 1rem;
}

.el-textarea__inner, .el-input__inner, .el-input-number__decrease, .el-input-number__increase {
  border-color: rgba(161, 196, 253, 0.3);
}

.el-textarea__inner:focus, .el-input__inner:focus {
  border-color: #a1c4fd;
  box-shadow: 0 0 0 2px rgba(161, 196, 253, 0.2);
}

/* 失败惩罚对话框 */
.failure-dialog .el-dialog__header {
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
}

.failure-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 30px 0;
  position: relative;
  overflow: hidden;
}

.failure-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><path d="M0,0 L200,200 M0,200 L200,0" stroke="rgba(239, 68, 68, 0.05)" stroke-width="1" stroke-dasharray="5,5"/></svg>');
  background-size: 200px 200px;
  pointer-events: none;
}

.failure-icon {
  width: 120px;
  height: 120px;
  position: relative;
  margin-bottom: 30px;
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
  animation-delay: 0.5s;
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(4px, 0, 0); }
}

.crack-large {
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path d="M50,0 L40,20 L60,30 L30,50 L70,70 L50,100 M20,30 L40,50 M60,50 L80,30" stroke="rgba(239, 68, 68, 0.8)" stroke-width="3" fill="none"/><path d="M50,0 L55,15 L45,25 L65,45 L40,65 L50,100" stroke="rgba(239, 68, 68, 0.4)" stroke-width="2" fill="none"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 0 10px rgba(239, 68, 68, 0.5));
  animation: crackGlow 2s infinite alternate;
}

@keyframes crackGlow {
  0% { filter: drop-shadow(0 0 5px rgba(239, 68, 68, 0.3)); }
  100% { filter: drop-shadow(0 0 15px rgba(239, 68, 68, 0.7)); }
}

.failure-title {
  font-size: 2rem;
  color: #ef4444;
  margin-bottom: 15px;
  font-weight: 600;
  letter-spacing: 1px;
  text-shadow: 0 2px 5px rgba(239, 68, 68, 0.2);
  position: relative;
  display: inline-block;
}

.failure-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, transparent, #ef4444, transparent);
  border-radius: 3px;
}

.failure-description {
  color: #555;
  margin-bottom: 30px;
  font-size: 1.1rem;
  max-width: 80%;
}

.penalties {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 500px;
  margin-bottom: 30px;
}

.penalty-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border-radius: 16px;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.penalty-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: translateX(-100%) rotate(45deg);
  animation: shimmerPenalty 3s infinite;
  z-index: 1;
}

@keyframes shimmerPenalty {
  0% { transform: translateX(-100%) rotate(45deg); }
  50%, 100% { transform: translateX(100%) rotate(45deg); }
}

.penalty-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.2);
}

.penalty-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.2);
  position: relative;
  z-index: 2;
}

.points-penalty::before {
  content: "-";
  color: #ef4444;
  font-size: 2rem;
  font-weight: bold;
}

.crack-penalty::before {
  content: "✓";
  color: #ef4444;
  font-size: 1.5rem;
}

.streak-penalty::before {
  content: "0";
  color: #ef4444;
  font-size: 1.5rem;
  font-weight: bold;
}

.penalty-details {
  flex: 1;
  position: relative;
  z-index: 2;
}

.penalty-details h4 {
  margin: 0 0 8px 0;
  color: #b91c1c;
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.penalty-details p {
  margin: 0;
  color: #555;
  font-size: 1rem;
  line-height: 1.5;
}

.failure-quote {
  font-style: italic;
  color: #555;
  max-width: 500px;
  margin-top: 30px;
  padding: 20px;
  border-left: 4px solid rgba(239, 68, 68, 0.3);
  background: rgba(254, 242, 242, 0.5);
  border-radius: 0 16px 16px 0;
  font-size: 1.05rem;
  line-height: 1.6;
  text-align: left;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}

.failure-quote::before {
  content: '"';
  position: absolute;
  top: 10px;
  left: 20px;
  font-size: 3rem;
  color: rgba(239, 68, 68, 0.1);
  font-family: Georgia, serif;
}

/* 动画 */
@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@keyframes flicker {
  0% { opacity: 0.8; transform: scale(0.95); }
  100% { opacity: 1; transform: scale(1.05); }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .stats-area {
    width: 100%;
    justify-content: space-between;
  }
  
  .stat-card {
    width: 30%;
  }
  
  .day {
    padding: 5px;
  }
  
  .day-number {
    font-size: 0.9rem;
  }
  
  .task-actions {
    flex-direction: column;
  }
  
  .action-button {
    width: 100%;
  }
}

/* 更小屏手机端适配（精致小卡片） */
@media (max-width: 480px) {
  .my-tasks-container {
    padding: 12px;
  }

  .header-card {
    border-radius: 16px;
    padding: 18px;
  }

  .header-title h1 {
    font-size: 1.6rem;
  }

  .header-title p {
    font-size: 0.9rem;
  }

  .stats-row {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .stat-box {
    min-width: auto;
    padding: 8px 10px;
    border-radius: 10px;
  }

  .stat-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }

  .completion-rate {
    width: 24px;
    height: 24px;
  }

  .stat-value {
    font-size: 1.1rem;
    margin-bottom: 1px;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}
/* 横向任务卡片样式 */
.modern-task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 4px;
}

.horizontal-task-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.04);
  min-height: 80px;
}

.horizontal-task-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: rgba(64, 158, 255, 0.15);
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6px;
  border-radius: 0 4px 4px 0;
}

.status-indicator.status-pending {
  background: linear-gradient(135deg, #909399, #b1b3b8);
}

.status-indicator.status-in-progress {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.status-indicator.status-completed {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.status-indicator.status-failed {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

/* 横向布局主体 */
.task-main-content {
  padding: 16px 20px;
  margin-left: 6px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 第一行：标题、标签、积分 */
.task-header-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  flex-shrink: 0;
}

.task-tags-inline {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  flex: 1;
}

.points-display {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

/* 第二行：信息 */
.task-info-row {
  display: flex;
  align-items: center;
}

.task-meta-inline {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-text {
  font-size: 13px;
  color: #6b7280;
}

/* 第三行：操作按钮 */
.task-actions-row {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

/* 横向按钮样式 */
.horizontal-btn {
  border-radius: 8px !important;
  padding: 6px 16px !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  transition: all 0.3s ease !important;
  border: 1px solid transparent !important;
  min-width: 60px;
}

.horizontal-btn:hover {
  transform: translateY(-1px);
}

.primary-btn {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  border-color: transparent !important;
  color: white !important;
}

.secondary-btn {
  background: #f8fafc !important;
  color: #374151 !important;
  border-color: #e5e7eb !important;
}

.danger-btn {
  color: #dc2626 !important;
  border-color: #fca5a5 !important;
}

.danger-btn:hover {
  background: #fee2e2 !important;
}

/* 已提交状态显示 */
.submitted-info-enhanced {
  background: linear-gradient(135deg, #fefce8, #fef3c7);
  border: 1px solid #fbbf24;
  border-radius: 12px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.submitted-badge {
  display: flex;
  align-items: center;
  gap: 6px;
}

.submitted-icon {
  font-size: 16px;
}

.submitted-text {
  color: #d97706;
  font-weight: 700;
  font-size: 14px;
}

.submitted-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.status-text {
  color: #d97706;
  font-weight: 600;
  background: #fef3c7;
  padding: 3px 8px;
  border-radius: 8px;
  align-self: flex-start;
}

.submitted-date {
  color: #6b7280;
}

.submitted-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

/* 增强的完成状态显示 */
.completion-info-enhanced {
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  border: 1px solid #86efac;
  border-radius: 12px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.completion-badge {
  display: flex;
  align-items: center;
  gap: 6px;
}

.completion-icon {
  font-size: 16px;
}

.completion-text {
  color: #059669;
  font-weight: 700;
  font-size: 14px;
}

.completion-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.points-earned {
  color: #059669;
  font-weight: 600;
  background: #dcfce7;
  padding: 3px 8px;
  border-radius: 8px;
  align-self: flex-start;
}

.completion-date {
  color: #6b7280;
}

.completion-rating {
  color: #f59e0b;
  font-weight: 500;
}

.completion-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

/* 紧凑的完成状态显示 */
.completion-info-compact {
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  border: 1px solid #86efac;
  border-radius: 8px;
  padding: 10px 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  height: 40px;
  width: 100%;
  box-sizing: border-box;
}

.completion-info-compact > * {
  margin: 0;
  padding: 0;
  vertical-align: middle;
  line-height: 1;
}

.completion-info-compact .completion-icon {
  font-size: 16px;
}

.completion-info-compact .completion-text {
  font-weight: 600;
  color: #166534;
  font-size: 14px;
}

.completion-info-compact .points-earned {
  color: #dc2626;
  font-weight: 600;
  font-size: 14px;
  /* {{ AURA-X: Modify - 向下移动2像素. Approval: 寸止(ID:1734684004). }} */
  transform: translateY(2px);
}

.completion-info-compact .completion-rating {
  color: #f59e0b;
  font-size: 14px;
}

.details-btn-compact {
  margin-left: auto;
  font-size: 12px;
  padding: 2px 8px;
  height: 20px;
  line-height: 1;
  vertical-align: middle;
}

/* 紧凑的提交状态显示 */
.submitted-info-compact {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 10px 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  height: 40px;
  width: 100%;
  box-sizing: border-box;
}

.submitted-info-compact > * {
  margin: 0;
  padding: 0;
  vertical-align: middle;
  line-height: 1;
}

.submitted-info-compact .submitted-icon {
  font-size: 16px;
}

.submitted-info-compact .submitted-text {
  font-weight: 600;
  color: #92400e;
  font-size: 14px;
}

.submitted-info-compact .status-text {
  color: #d97706;
  font-size: 14px;
  /* {{ AURA-X: Modify - 向下移动2像素. Approval: 寸止(ID:1734684005). }} */
  transform: translateY(2px);
}

/* {{ AURA-X: Add - 添加submitted状态的按钮布局样式. Approval: 寸止(ID:1734684014). }} */
.submitted-actions {
  display: flex;
  gap: 6px;
  margin-left: auto;
}

/* {{ AURA-X: Add - 添加rejected状态样式. Approval: 寸止(ID:1734684008). }} */
.rejected-info-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.rejected-icon {
  font-size: 16px;
}

.rejected-text {
  color: #dc2626;
  font-weight: 600;
  font-size: 14px;
}

.rejection-reason {
  color: #7f1d1d;
  font-size: 12px;
  background: #fee2e2;
  padding: 2px 6px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rejected-actions {
  display: flex;
  gap: 6px;
  margin-left: auto;
}

.resubmit-btn-compact {
  font-size: 12px;
  padding: 4px 8px;
  height: 24px;
}

/* 紧凑版失败状态卡片 */
.failure-compact-card {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 1px solid #f87171;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(248, 113, 113, 0.15);
  position: relative;
  overflow: hidden;
}

.failure-compact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* 紧凑版失败信息行 */
.failure-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.failure-badge-compact {
  display: flex;
  align-items: center;
  gap: 6px;
}

.failure-icon-small {
  font-size: 16px;
}

.failure-text-compact {
  font-size: 14px;
  font-weight: 700;
  color: #dc2626;
}

.failure-details-compact {
  display: flex;
  gap: 12px;
  align-items: center;
}

.penalty-compact {
  background: #fee2e2;
  color: #dc2626;
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.time-compact {
  color: #6b7280;
  font-size: 12px;
}

/* 紧凑版操作按钮 */
.failure-actions-compact {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.compact-btn {
  border-radius: 8px !important;
  padding: 6px 12px !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  transition: all 0.3s ease !important;
}

.retry-compact {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  border-color: transparent !important;
  color: white !important;
}

.retry-compact:hover {
  background: linear-gradient(135deg, #d97706, #b45309) !important;
  transform: translateY(-1px);
}

.details-compact {
  background: rgba(255, 255, 255, 0.8) !important;
  border-color: #d1d5db !important;
  color: #6b7280 !important;
}

.details-compact:hover {
  background: white !important;
  border-color: #9ca3af !important;
  color: #374151 !important;
}

.test-btn {
  color: #6b7280 !important;
  border-color: #d1d5db !important;
  font-size: 11px !important;
  padding: 4px 8px !important;
}

.test-btn:hover {
  background: #f3f4f6 !important;
  color: #374151 !important;
}

.tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid transparent;
}

.tag-type {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.tag-subject {
  background: #f8f9fa;
  color: #495057;
  border-color: #e9ecef;
}

.tag-subject.subject-math {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: #721c24;
}

.tag-subject.subject-english {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #0c4a6e;
}

.tag-subject.subject-politics {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  color: #7c2d12;
}

.tag-subject.subject-major {
  background: linear-gradient(135deg, #c3cfe2, #c3cfe2);
  color: #374151;
}

.tag-status {
  font-weight: 600;
}

.tag-status.status-pending {
  background: #f3f4f6;
  color: #6b7280;
}

.tag-status.status-in-progress {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
}

.tag-status.status-completed {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #166534;
}

.tag-status.status-failed {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
}

.points-section {
  margin-left: 16px;
}

.points-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 6px 12px;
  border-radius: 14px;
  font-weight: 600;
  box-shadow: 0 3px 8px rgba(251, 191, 36, 0.3);
}

.points-icon {
  font-size: 16px;
}

.points-value {
  font-size: 14px;
}

/* 任务描述 - 紧凑版 */
.task-description {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 14px;
  padding: 12px 14px;
  background: #f8fafc;
  border-radius: 10px;
  border-left: 3px solid #e5e7eb;
}

/* 任务元信息 - 紧凑版 */
.task-meta {
  margin-bottom: 16px;
}

.meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  background: #f8fafc;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}

.meta-icon {
  color: #6b7280;
  font-size: 16px;
}

.meta-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.meta-label {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

.meta-value {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.penalty-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  border-radius: 12px;
  border: 1px solid #fca5a5;
}

.penalty-icon {
  color: #dc2626;
  font-size: 16px;
}

.penalty-text {
  color: #dc2626;
  font-weight: 600;
  font-size: 14px;
}

/* 进度条部分 - 紧凑版 */
.progress-section {
  margin-bottom: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.progress-percentage {
  font-size: 16px;
  font-weight: 700;
  color: #059669;
}

.progress-container {
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

/* 操作按钮 - 紧凑版 */
.action-section {
  border-top: 1px solid #f3f4f6;
  padding-top: 16px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.modern-btn {
  border-radius: 10px !important;
  padding: 10px 16px !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  transition: all 0.3s ease !important;
  border: 2px solid transparent !important;
  display: flex;
  align-items: center;
  gap: 6px;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

.primary-btn {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  border-color: transparent !important;
}

.secondary-btn {
  background: #f8fafc !important;
  color: #374151 !important;
  border-color: #e5e7eb !important;
}

.danger-btn {
  color: #dc2626 !important;
  border-color: #fca5a5 !important;
}

.danger-btn:hover {
  background: #fee2e2 !important;
}

/* 紧凑版整体优化 */
.compact-calendar .content-card {
  padding: 20px !important;
}

.compact-header {
  margin-bottom: 16px !important;
}

.compact-nav-btn {
  padding: 8px !important;
  min-height: 32px !important;
}

/* 日历紧凑优化 */
.calendar .day {
  height: 45px !important;
  min-height: 45px !important;
}

.day-number {
  font-size: 13px !important;
}

/* 任务卡片区域紧凑优化 */
.tasks-card .content-card {
  padding: 20px !important;
}

.tasks-card .card-header {
  margin-bottom: 16px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
  }

  .points-section {
    margin-left: 0;
    align-self: flex-start;
  }

  .meta-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .modern-btn {
    justify-content: center;
  }

  .modern-task-card {
    margin: 0 -4px;
  }

  .card-body {
    padding: 16px 18px;
  }
}

/* 紧凑版失败对话框样式 */
.modern-failure-content {
  padding: 24px;
  background: linear-gradient(135deg, #fefefe, #f8fafc);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.modern-failure-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ef4444, #dc2626, #b91c1c);
}

/* 头部区域 - 紧凑版 */
.failure-header {
  text-align: center;
  margin-bottom: 20px;
}

.failure-icon-modern {
  font-size: 36px;
  margin-bottom: 12px;
  display: block;
}

.failure-title-modern {
  font-size: 20px;
  font-weight: 700;
  color: #dc2626;
  margin: 0 0 8px 0;
}

.failure-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.task-name {
  color: #374151;
  font-weight: 600;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 6px;
}

/* 后果说明区域 - 紧凑版 */
.consequences-section {
  margin-bottom: 18px;
}

.consequences-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  text-align: center;
}

.penalty-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}

.penalty-card {
  background: white;
  border-radius: 10px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.penalty-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.points-penalty::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.crack-penalty::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.streak-penalty::before {
  background: linear-gradient(90deg, #6b7280, #4b5563);
}

.penalty-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.penalty-card-icon {
  font-size: 20px;
  margin-bottom: 6px;
  display: block;
}

.penalty-card-content h5 {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 3px 0;
}

.penalty-value {
  font-size: 14px;
  font-weight: 700;
  color: #dc2626;
  margin: 0 0 3px 0;
}

.penalty-desc {
  font-size: 11px;
  color: #6b7280;
}

/* 提示信息 - 紧凑版 */
.failure-tip {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
  border-radius: 10px;
  padding: 12px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.tip-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.failure-tip p {
  margin: 0;
  color: #92400e;
  font-size: 13px;
  line-height: 1.4;
}

/* 紧凑版按钮区域 */
.modern-dialog-footer {
  display: flex;
  gap: 10px;
  justify-content: center;
  padding: 12px 0 0 0;
}

.cancel-btn {
  background: #f8fafc !important;
  border-color: #e5e7eb !important;
  color: #374151 !important;
  font-weight: 600 !important;
  padding: 10px 20px !important;
  border-radius: 10px !important;
  font-size: 14px !important;
}

.cancel-btn:hover {
  background: #f1f5f9 !important;
  border-color: #d1d5db !important;
}

.confirm-abandon-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  border-color: transparent !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 10px 20px !important;
  border-radius: 10px !important;
  font-size: 14px !important;
}

.confirm-abandon-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3) !important;
}
</style>
