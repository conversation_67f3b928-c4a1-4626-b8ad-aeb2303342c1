// {{ AURA-X: Add - 创建统一的学生端数据管理. Approval: 寸止(ID:1734684000). }}
// 学生端统一数据管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { bindingAPI } from '@/api/binding'
import { taskAPI } from '@/api/task'
import { useAuthStore } from './auth'
import { useSyncStore } from './sync'

export const useStudentStore = defineStore('student', () => {
  // 状态
  const isLoading = ref(false)
  const lastUpdateTime = ref(null)
  
  // 绑定信息 - 添加持久化
  const bindingInfo = ref(JSON.parse(localStorage.getItem('student_bindingInfo') || 'null'))
  
  // 任务统计数据 - 添加持久化
  const taskStats = ref(JSON.parse(localStorage.getItem('student_taskStats') || JSON.stringify({
    accepted_tasks: 0,
    completed_tasks: 0,
    total_points: 0,
    completion_rate: 0,
    total_study_time: 0
  })))
  
  // 我的任务列表 - 添加持久化
  const myTasks = ref(JSON.parse(localStorage.getItem('student_myTasks') || '[]'))
  
  // 可用任务列表 - 添加持久化
  const availableTasks = ref(JSON.parse(localStorage.getItem('student_availableTasks') || '[]'))

  // 计算属性
  const isAuthenticated = computed(() => {
    const authStore = useAuthStore()
    return authStore.isAuthenticated && authStore.isStudent
  })

  const hasBinding = computed(() => {
    return bindingInfo.value && bindingInfo.value.status === 'active'
  })

  const supervisorInfo = computed(() => {
    return bindingInfo.value?.supervisor || null
  })

  const todayPoints = computed(() => {
    // 严格以明确的“今日完成日期”统计今日积分（不再将缺失日期默认视为今日）
    const today = new Date()
    const isSameDay = (d) => {
      if (!d) return false
      const dt = new Date(d)
      return !isNaN(dt.getTime()) && dt.toDateString() === today.toDateString()
    }

    console.log('计算今日积分，任务数量:', myTasks.value.length)

    const todayCompletedTasks = myTasks.value.filter(task => {
      if (task.status !== 'completed') return false
      // 兼容后端字段：completedAt/completionDate/endTime/reviewDate
      const completedDate = task.completedAt || task.completionDate || task.endTime || task.reviewDate
      const ok = isSameDay(completedDate)
      return ok
    })

    const points = todayCompletedTasks.reduce((total, task) => total + (task.points || 0), 0)
    console.log('今日完成任务:', todayCompletedTasks.length, '总积分:', points)
    return points
  })

  // {{ AURA-X: Add - 添加今日完成任务数计算. Approval: 寸止(ID:1734683400). }}
  const todayCompletedTasks = computed(() => {
    // 计算今日完成的任务数
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())

    return myTasks.value.filter(task => {
      // 兼容多种日期字段名
      const completedDate = task.completedAt || task.completionDate || task.endTime
      const dateObj = completedDate ? new Date(completedDate) : null
      const isCompleted = task.status === 'completed'
      // 检查是否是今天完成的
      const isToday = dateObj ? dateObj >= todayStart : false

      return isCompleted && isToday
    }).length
  })

  const weeklyCompletedTasks = computed(() => {
    // 计算本周完成的任务数
    const now = new Date()
    const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())

    return myTasks.value.filter(task => {
      const completedDate = task.completedAt ? new Date(task.completedAt) : null
      return task.status === 'completed' &&
             completedDate &&
             completedDate >= weekStart
    }).length
  })

  const studyHoursToday = computed(() => {
    // 计算今日学习时长（小时）
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    
    const todayTasks = myTasks.value.filter(task => {
      const completedDate = task.completedAt ? new Date(task.completedAt) : null
      return task.status === 'completed' && 
             completedDate && 
             completedDate >= todayStart
    })
    
    const totalMinutes = todayTasks.reduce((total, task) => total + (task.timeSpent || 0), 0)
    return Math.round(totalMinutes / 60 * 10) / 10
  })

  // 方法



  /**
   * 初始化学生数据
   */
  const initStudentData = async () => {
    if (!isAuthenticated.value) {
      console.warn('用户未认证，无法初始化学生数据')
      return
    }

    try {
      isLoading.value = true

      // 并行获取数据
      await Promise.all([
        loadBindingInfo(),
        loadMyTasks(),
        loadAvailableTasks()
      ])
      
      lastUpdateTime.value = new Date()
      // 保存最后更新时间到本地存储
      localStorage.setItem('student_lastUpdateTime', lastUpdateTime.value.toISOString())

      // 禁用实时同步，仅使用手动刷新
      // const syncStore = useSyncStore()
      // if (!syncStore.isInitialized) {
      //   await syncStore.initSync()
      //   syncStore.startUserSubscriptions()
      // }

      console.log('学生数据初始化完成，实时同步已禁用')

    } catch (error) {
      console.error('初始化学生数据失败:', error)
      ElMessage.error('数据加载失败，请刷新页面重试')
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 启用实时同步
   */
  const startRealtimeSync = () => {
    // 监听任务数据变更
    window.addEventListener('tasks-updated', (event) => {
      console.log('接收到任务实时更新:', event.detail)
      if (event.detail) {
        myTasks.value = event.detail
      }
    })

    // 监听绑定信息和统计数据变更
    window.addEventListener('bindings-updated', (event) => {
      console.log('接收到绑定信息实时更新:', event.detail)
      if (event.detail) {
        // 更新绑定信息和统计数据
        bindingInfo.value = event.detail.binding || null
        taskStats.value = event.detail.stats || {}
      }
    })

    // 监听学生统计数据变更
    window.addEventListener('student-stats-updated', (event) => {
      console.log('接收到学生统计实时更新:', event.detail)
      // 重新加载绑定信息以获取最新统计
      loadBindingInfo()
    })

    console.log('实时同步已启用');
  }

  /**
   * 加载绑定信息和任务统计
   */
  const loadBindingInfo = async () => {
    try {
      const response = await bindingAPI.getMyBinding();

      if (response && response.data) {
        bindingInfo.value = response.data;
        // 保存到本地存储
        localStorage.setItem('student_bindingInfo', JSON.stringify(bindingInfo.value));

        // 更新任务统计
        if (response.data.task_stats) {
          taskStats.value = {
            accepted_tasks: response.data.task_stats.accepted_tasks || 0,
            completed_tasks: response.data.task_stats.completed_tasks || 0,
            total_points: response.data.task_stats.total_points || 0,
            completion_rate: response.data.task_stats.completion_rate || 0,
            total_study_time: response.data.task_stats.total_study_time || 0
          };
          // 保存到本地存储
          localStorage.setItem('student_taskStats', JSON.stringify(taskStats.value));
        }

        console.log('绑定信息已更新:', bindingInfo.value);
        console.log('任务统计已更新:', taskStats.value);
      } else {
        console.log('API响应无数据，使用本地缓存数据');
        // 如果API失败但有本地缓存，保留本地缓存
        if (!bindingInfo.value) {
          // 尝试从本地存储恢复
          const cachedBindingInfo = localStorage.getItem('student_bindingInfo');
          if (cachedBindingInfo && cachedBindingInfo !== 'null') {
            try {
              bindingInfo.value = JSON.parse(cachedBindingInfo);
              console.log('从本地存储恢复了绑定信息');
            } catch (e) {
              console.error('解析本地存储的绑定信息失败:', e);
              bindingInfo.value = null;
            }
          } else {
            bindingInfo.value = null;
            console.log('未找到绑定信息');
          }
        }
      }
    } catch (error) {
      console.error('加载绑定信息失败:', error);
      
      // 尝试从本地存储恢复
      if (!bindingInfo.value) {
        const cachedBindingInfo = localStorage.getItem('student_bindingInfo');
        if (cachedBindingInfo && cachedBindingInfo !== 'null') {
          try {
            bindingInfo.value = JSON.parse(cachedBindingInfo);
            console.log('从本地存储恢复了绑定信息');
          } catch (e) {
            console.error('解析本地存储的绑定信息失败:', e);
            bindingInfo.value = null;
          }
        }
      }
      
      if (error.response?.status !== 404) {
        console.warn('非404错误，但将继续使用本地缓存数据');
      }
    }
  };

  /**
   * 加载我的任务
   */
  const loadMyTasks = async () => {
    try {
      console.log('开始加载我的任务...')
      const response = await taskAPI.getMyTasks()

      console.log('getMyTasks API响应:', response)

      if (response && response.data) {
        // 保留现有任务的本地状态，只更新来自服务器的数据
        const serverTasks = response.data
        const currentTasks = myTasks.value

        // {{ AURA-X: Modify - 修复数据合并逻辑，允许rejected状态覆盖本地状态. Approval: 寸止(ID:1734684020). }}
        // 合并任务数据：服务器状态优先，特别是rejected状态
        const mergedTasks = serverTasks.map(serverTask => {
          const localTask = currentTasks.find(t =>
            (t.id === serverTask.id || t._id === serverTask._id) ||
            (t.id === serverTask._id || t._id === serverTask.id)
          )

      if (localTask) {
        // 如果本地有任务记录
        if (serverTask.status === 'completed') {
          // 如果服务器显示任务已完成，优先使用服务器状态
          console.log(`任务 ${serverTask.title} 状态已从 ${localTask.status} 更新为 completed`);
          return {
            ...localTask,
            status: 'completed',
            completedAt: serverTask.completedAt || new Date().toISOString()
          }
        } else if (serverTask.status === 'rejected') {
          // 如果服务器显示任务被拒绝，优先使用服务器状态
          console.log(`任务 ${serverTask.title} 状态已从 ${localTask.status} 更新为 rejected`);
          return {
            ...localTask,
            ...serverTask, // 使用服务器的完整数据，包括拒绝原因
            status: 'rejected'
          }
        }
      }
      // 其他情况使用服务器数据
      return serverTask
        })

        myTasks.value = mergedTasks
        
        // 保存到本地存储
        localStorage.setItem('student_myTasks', JSON.stringify(myTasks.value))
        
        console.log('我的任务已更新:', myTasks.value.length, '个任务')
        console.log('任务详情:', myTasks.value)
      } else {
        console.log('API响应无数据，使用本地缓存数据')
        // 如果API失败但有本地缓存，保留本地缓存
        if (myTasks.value.length === 0) {
          // 只有在本地也没有数据时才设为空数组
          myTasks.value = []
          localStorage.setItem('student_myTasks', JSON.stringify([]))
        }
      }
    } catch (error) {
      console.error('加载我的任务失败:', error)
      console.error('错误详情:', error.response?.data || error.message)
      
      // 如果API请求失败但有本地缓存，保留本地缓存
      if (myTasks.value.length === 0) {
        // 尝试从本地存储恢复
        const cachedTasks = localStorage.getItem('student_myTasks')
        if (cachedTasks) {
          try {
            myTasks.value = JSON.parse(cachedTasks)
            console.log('从本地存储恢复了任务数据:', myTasks.value.length, '个任务')
          } catch (e) {
            console.error('解析本地存储的任务数据失败:', e)
            myTasks.value = []
          }
        } else {
          myTasks.value = []
        }
      }
      
      // 不抛出错误，允许使用本地缓存继续操作
      console.log('将使用本地缓存的任务数据继续操作')
    }
  }

  /**
   * 加载可用任务
   */
  const loadAvailableTasks = async () => {
    try {
      const response = await taskAPI.getFixedTasks()
      
      if (response && response.data) {
        availableTasks.value = response.data
        // 保存到本地存储
        localStorage.setItem('student_availableTasks', JSON.stringify(availableTasks.value))
        console.log('可用任务已更新:', availableTasks.value.length, '个任务')
      } else {
        console.log('API响应无数据，使用本地缓存数据')
        // 如果API失败但有本地缓存，保留本地缓存
        if (availableTasks.value.length === 0) {
          // 尝试从本地存储恢复
          const cachedTasks = localStorage.getItem('student_availableTasks')
          if (cachedTasks) {
            try {
              availableTasks.value = JSON.parse(cachedTasks)
              console.log('从本地存储恢复了可用任务数据:', availableTasks.value.length, '个任务')
            } catch (e) {
              console.error('解析本地存储的可用任务数据失败:', e)
              availableTasks.value = []
            }
          } else {
            availableTasks.value = []
          }
        }
      }
    } catch (error) {
      console.error('加载可用任务失败:', error)
      
      // 尝试从本地存储恢复
      const cachedTasks = localStorage.getItem('student_availableTasks')
      if (cachedTasks) {
        try {
          availableTasks.value = JSON.parse(cachedTasks)
          console.log('从本地存储恢复了可用任务数据:', availableTasks.value.length, '个任务')
        } catch (e) {
          console.error('解析本地存储的可用任务数据失败:', e)
          availableTasks.value = []
        }
      } else {
        availableTasks.value = []
      }
    }
  }

  /**
   * 接受任务
   */
  const acceptTask = async (task) => {
    try {
      // 修复API调用格式 - 后端期望 { taskData: task }
      const response = await taskAPI.acceptFixedTask(task)

      if (response && response.data) {
        // 重新加载我的任务和可用任务
        await Promise.all([
          loadMyTasks(),
          loadAvailableTasks()
        ])

        ElMessage.success('任务接受成功')
        return true
      } else {
        ElMessage.error('任务接受失败：服务器响应异常')
        return false
      }
    } catch (error) {
      console.error('接受任务失败:', error)
      const errorMessage = error.response?.data?.message || error.message || '接受任务失败'
      ElMessage.error(errorMessage)
      return false
    }
  }

  /**
   * 提交任务完成
   */
  const submitTaskCompletion = async (taskId, completionData) => {
    try {
      console.log('=== 前端提交任务调试 ===');
      console.log('任务ID:', taskId);
      console.log('提交数据:', completionData);

      const response = await taskAPI.submitTaskCompletion(taskId, completionData)
      console.log('API响应:', response);
      
      if (response && response.data) {
        // 只更新本地任务状态，不重新加载所有数据
        const taskIndex = myTasks.value.findIndex(task => task.id === taskId || task._id === taskId)
        if (taskIndex !== -1) {
          // 更新任务状态为已提交
          myTasks.value[taskIndex] = {
            ...myTasks.value[taskIndex],
            status: 'submitted',
            completionDate: new Date().toISOString(),
            ...completionData
          }
          
          // 保存到本地存储
          localStorage.setItem('student_myTasks', JSON.stringify(myTasks.value))
        }

        // 触发全局数据更新事件，同步到云端
        window.dispatchEvent(new CustomEvent('tasks-updated', {
          detail: myTasks.value
        }))

        // 更新任务统计数据
        taskStats.value.completed_tasks += 1
        taskStats.value.completion_rate = taskStats.value.completed_tasks / taskStats.value.accepted_tasks
        localStorage.setItem('student_taskStats', JSON.stringify(taskStats.value))

        ElMessage.success('任务提交成功')
        return true
      }
    } catch (error) {
      console.error('=== 提交任务失败调试 ===');
      console.error('错误详情:', error);
      console.error('错误响应:', error.response?.data);
      console.error('错误状态:', error.response?.status);
      
      // 即使API请求失败，也尝试更新本地状态
      // 这样用户可以看到他们的提交，即使暂时无法同步到云端
      const taskIndex = myTasks.value.findIndex(task => task.id === taskId || task._id === taskId)
      if (taskIndex !== -1) {
        // 更新任务状态为已提交，但标记为本地提交
        myTasks.value[taskIndex] = {
          ...myTasks.value[taskIndex],
          status: 'submitted',
          completionDate: new Date().toISOString(),
          localOnly: true, // 标记为本地提交，等待同步
          ...completionData
        }
        
        // 保存到本地存储
        localStorage.setItem('student_myTasks', JSON.stringify(myTasks.value))
        
        ElMessage.warning('已保存到本地，将在网络恢复后同步到云端')
        return true
      }
      
      ElMessage.error('提交失败，请重试')
      return false
    }
  }

  /**
   * 刷新所有数据
   */
  const refreshAllData = async () => {
    await initStudentData()
  }

  /**
   * 清理数据（用于登出）
   */
  const clearData = () => {
    // 清理内存中的数据
    bindingInfo.value = null
    taskStats.value = {
      accepted_tasks: 0,
      completed_tasks: 0,
      total_points: 0,
      completion_rate: 0,
      total_study_time: 0
    }
    myTasks.value = []
    availableTasks.value = []
    lastUpdateTime.value = null
    
    // 清理本地存储中的数据
    localStorage.removeItem('student_bindingInfo')
    localStorage.removeItem('student_taskStats')
    localStorage.removeItem('student_myTasks')
    localStorage.removeItem('student_availableTasks')
    localStorage.removeItem('student_lastUpdateTime')
    
    // 停止实时同步
    const syncStore = useSyncStore()
    syncStore.stopAllSubscriptions()
    
    console.log('学生数据已清理（内存和本地存储）')
  }

  /**
   * 获取任务详情
   */
  const getTaskById = (taskId) => {
    return myTasks.value.find(task => task.id === taskId) || 
           availableTasks.value.find(task => task.id === taskId)
  }

  return {
    // 状态
    isLoading,
    lastUpdateTime,
    bindingInfo,
    taskStats,
    myTasks,
    availableTasks,
    
    // 计算属性
    isAuthenticated,
    hasBinding,
    supervisorInfo,
    todayPoints,
    todayCompletedTasks,
    weeklyCompletedTasks,
    studyHoursToday,
    
    // 方法
    initStudentData,
    loadBindingInfo,
    loadMyTasks,
    loadAvailableTasks,
    acceptTask,
    submitTaskCompletion,
    refreshAllData,
    clearData,
    getTaskById
  }
})
